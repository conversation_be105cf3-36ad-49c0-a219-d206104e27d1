# Vue.js and React Dependency Analyzer

A comprehensive Python tool that uses tree-sitter-typescript to parse Vue.js and React project source code and extract dependency relationships. This tool provides detailed analysis of component hierarchies, import/export relationships, and dependency graphs with multiple output formats.

## Features

### 🔍 **Multi-Framework Support**
- **Vue.js**: Parse `.vue` Single File Components with `<script>`, `<template>`, and `<style>` sections
- **React**: Analyze `.jsx`, `.tsx`, `.ts`, and `.js` files with JSX component usage
- **TypeScript**: Full TypeScript support with type-only imports detection

### 📊 **Dependency Analysis**
- **Import Detection**: ES6 imports, CommonJS require, dynamic imports, type-only imports
- **Component Relationships**: Vue component registrations, React component usage
- **Path Resolution**: Relative imports, absolute imports, and external dependencies
- **Circular Dependencies**: Automatic detection and reporting with detailed cycle paths
- **Dependency Metrics**: Complexity analysis, architectural insights, maintainability scores

### 📈 **Visualization & Output**
- **JSON**: Structured data with detailed import/export information
- **DOT**: Graphviz format for professional dependency graphs
- **Mermaid**: Interactive diagrams for documentation
- **Text Tree**: Human-readable hierarchical structure
- **CSV**: Tabular data for spreadsheet analysis
- **XML**: Structured markup for integration with other tools
- **SARIF**: Static analysis results format for CI/CD integration
- **Markdown Reports**: Comprehensive analysis reports with metrics

### 🛠 **Advanced Features**
- **Entry Point Detection**: Automatically identify application entry points
- **Leaf Node Analysis**: Find components with no dependencies
- **Statistics**: Comprehensive metrics about your project structure
- **Path Analysis**: Find dependency paths between any two files
- **Performance Metrics**: Bundle analysis, loading chains, optimization opportunities
- **Quality Assessment**: Code smells detection, best practices scoring
- **Configuration Management**: Flexible configuration with multiple sources

## Installation

### Using uv (Recommended)

```bash
# Clone the repository
git clone <repository-url>
cd diff_analyzer

# Install the package and dependencies
uv install

# Install with development dependencies
uv install --group test --group dev
```

### Using pip

```bash
# Clone the repository
git clone <repository-url>
cd diff_analyzer

# Install in development mode
pip install -e .

# Install with test dependencies
pip install -e ".[test]"

# Install with all dependencies
pip install -e ".[test,dev]"
```

## Quick Start

### Basic Analysis

```bash
# Analyze a Vue.js project
uv run python -m src.diff_analyzer.main ./my-vue-project

# Or using the CLI script
./diff_analyzer ./my-vue-project

# Analyze a React project with verbose output
uv run python -m src.diff_analyzer.main ./my-react-app --verbose

# Generate JSON output
uv run python -m src.diff_analyzer.main ./project --format json --output dependencies.json
```

### Advanced Usage

```bash
# Generate Mermaid diagram
uv run python -m src.diff_analyzer.main ./project --format mermaid --output diagram.md

# Create Graphviz DOT file
uv run python -m src.diff_analyzer.main ./project --format dot --output graph.dot

# Show detailed information
uv run python -m src.diff_analyzer.main ./project --show-details

# Exclude specific directories
uv run python -m src.diff_analyzer.main ./project --exclude node_modules dist build

# Show only statistics
uv run python -m src.diff_analyzer.main ./project --stats-only
```

### Using with pip installation

```bash
# If installed with pip, you can use the package directly
python -m src.diff_analyzer.main ./project --format json
```

## Output Formats

### 1. Text Tree (Default)
```
Dependency Tree Structure
==================================================

Total Files: 13
Total Dependencies: 22
Entry Points: 4
Leaf Nodes: 3

Entry Points:
--------------------
🚀 src/main.ts
├──
│   📁 src/App.vue
│   ├──
│   │   📁 src/components/UserProfile.vue
│   └──
│       📁 src/store/index.ts
```

### 2. JSON Format
```json
{
  "project_root": "/path/to/project",
  "statistics": {
    "total_files": 13,
    "total_dependencies": 22,
    "entry_points": 4,
    "circular_dependencies": 0
  },
  "files": {
    "src/main.ts": {
      "dependencies": ["src/App.vue", "src/router/index.ts"],
      "imports": [
        {
          "module": "vue",
          "type": "named",
          "dependency_type": "external",
          "imported_names": ["createApp"]
        }
      ]
    }
  }
}
```

### 3. Mermaid Diagram
```mermaid
flowchart TD
  N0["src/main.ts"]
  N1["src/App.vue"]
  N2["src/components/UserProfile.vue"]

  N0 --> N1
  N1 --> N2
```

### 4. DOT (Graphviz)
```dot
digraph DependencyGraph {
  rankdir=TB;
  node [shape=box, style=filled];

  "src/main.ts" [fillcolor=lightgreen];
  "src/App.vue" -> "src/components/UserProfile.vue";
}
```

## Supported File Types

| Extension | Framework | Parser |
|-----------|-----------|---------|
| `.vue` | Vue.js | Vue SFC Parser |
| `.tsx` | React/TypeScript | TSX Parser |
| `.jsx` | React | JSX Parser |
| `.ts` | TypeScript | TypeScript Parser |
| `.js` | JavaScript | TypeScript Parser |

## Configuration Options

### Command Line Options

| Option | Description | Default |
|--------|-------------|---------|
| `--format` | Output format (text, json, dot, mermaid) | text |
| `--output` | Output file path | stdout |
| `--exclude` | Patterns to exclude | node_modules, .git, dist |
| `--show-details` | Include detailed import information | false |
| `--verbose` | Enable verbose logging | false |
| `--stats-only` | Show only statistics | false |

### Exclusion Patterns

By default, the tool excludes common build and dependency directories:
- `node_modules`
- `.git`
- `dist`
- `build`
- `.next` (Next.js)
- `.nuxt` (Nuxt.js)

## Examples

### Vue.js Project Analysis

```bash
# Basic Vue.js analysis with text output
uv run python -m diff_analyzer ./vue-project --format text

# Generate dependency graph for documentation
uv run python -m diff_analyzer ./vue-project --format mermaid --output docs/dependencies.md

# Find circular dependencies with detailed reporting
uv run python -m diff_analyzer ./vue-project --find-cycles --show-details

# Generate comprehensive metrics report
uv run python -m diff_analyzer ./vue-project --format markdown --include-metrics
```

### React Project Analysis

```bash
# Analyze React TypeScript project with detailed output
uv run python -m diff_analyzer ./react-ts-project --show-details --format json

# Export to multiple formats for CI/CD integration
uv run python -m diff_analyzer ./react-project --format sarif --output analysis.sarif
uv run python -m diff_analyzer ./react-project --format csv --output dependencies.csv

# Analyze specific file relationships
uv run python -m diff_analyzer analyze-file ./react-project src/components/App.tsx

# Performance and quality analysis
uv run python -m diff_analyzer ./react-project --format markdown --include-performance --include-quality
```

### Advanced Configuration

```bash
# Use custom configuration file
uv run python -m diff_analyzer ./project --config custom-config.json

# Exclude specific patterns and set custom extensions
uv run python -m diff_analyzer ./project \
  --exclude "node_modules,dist,*.test.ts" \
  --extensions ".vue,.tsx,.ts"

# Parallel processing with custom worker count
uv run python -m diff_analyzer ./large-project --max-workers 8 --verbose
```

## Project Structure

```
diff_analyzer/
├── src/
│   └── diff_analyzer/
│       ├── __init__.py         # Package initialization and exports
│       ├── main.py             # CLI interface and main application
│       ├── dependency_analyzer.py  # Core dependency extraction classes
│       ├── vue_parser.py       # Vue.js specific parser
│       ├── react_parser.py     # React/TypeScript parser
│       ├── dependency_graph.py # Graph analysis and algorithms
│       └── output_formatters.py # Multiple output format support
├── tests/                      # Comprehensive test suite
│   ├── conftest.py            # Test fixtures and configuration
│   ├── test_dependency_analyzer.py
│   ├── test_vue_parser.py
│   ├── test_react_parser.py
│   ├── test_dependency_graph.py
│   ├── test_output_formatters.py
│   └── test_integration.py
├── examples/                   # Example projects for testing
│   ├── vue_project/           # Example Vue.js project
│   └── react_project/         # Example React project
├── docs/                      # Documentation
│   ├── API.md                 # API reference
│   ├── DEVELOPER_GUIDE.md     # Developer and extension guide
│   └── TESTING_GUIDE.md       # Testing documentation
├── pyproject.toml             # Project configuration and dependencies
├── diff_analyzer              # CLI entry point script
└── README.md
```

## API Usage

You can also use the tool programmatically for integration into your own applications:

### Basic Analysis

```python
from diff_analyzer import DependencyGraph, JSONFormatter, Config

# Load configuration
config = Config("/path/to/project")
config.load_config()

# Analyze a project
graph = DependencyGraph("/path/to/project", config=config)
graph.analyze_project()

# Get basic statistics
stats = graph.get_statistics()
print(f"Total files: {stats['total_files']}")
print(f"Total dependencies: {stats['total_dependencies']}")
print(f"Entry points: {stats['entry_points']}")
print(f"Leaf nodes: {stats['leaf_nodes']}")

# Find circular dependencies
cycles = graph.find_circular_dependencies()
if cycles:
    print(f"⚠️  Found {len(cycles)} circular dependencies:")
    for cycle in cycles:
        print(f"  {' -> '.join(cycle)}")
```

### Advanced Metrics and Analysis

```python
from diff_analyzer import DependencyMetrics, MarkdownReportFormatter

# Get advanced metrics
metrics = DependencyMetrics(graph)
all_metrics = metrics.calculate_all_metrics()

# Access specific metric categories
complexity = all_metrics['complexity_metrics']
quality = all_metrics['quality_metrics']
performance = all_metrics['performance_metrics']

print(f"Cyclomatic complexity: {complexity['cyclomatic_complexity']}")
print(f"Code quality score: {quality['best_practices_score']}")

# Generate comprehensive report
formatter = MarkdownReportFormatter(graph)
report = formatter.format(
    include_metrics=True,
    include_recommendations=True
)

with open("analysis_report.md", "w") as f:
    f.write(report)
```

### Multiple Output Formats

```python
from diff_analyzer import (
    JSONFormatter, DOTFormatter, MermaidFormatter,
    CSVFormatter, XMLFormatter, SARIFFormatter
)

# JSON output with metrics
json_formatter = JSONFormatter(graph)
json_output = json_formatter.format(include_metrics=True)

# DOT format for Graphviz
dot_formatter = DOTFormatter(graph)
dot_output = dot_formatter.format()

# Mermaid diagram
mermaid_formatter = MermaidFormatter(graph)
mermaid_output = mermaid_formatter.format()

# CSV for spreadsheet analysis
csv_formatter = CSVFormatter(graph)
dependencies_csv = csv_formatter.format("dependencies")
files_csv = csv_formatter.format("files")

# SARIF for CI/CD integration
sarif_formatter = SARIFFormatter(graph)
sarif_output = sarif_formatter.format()
```

### Error Handling and Validation

```python
from diff_analyzer.exceptions import (
    DependencyAnalyzerError, CircularDependencyError,
    ValidationError, ConfigurationError
)

try:
    graph = DependencyGraph("/path/to/project")
    graph.analyze_project()

    # Check for errors during analysis
    if graph.has_errors():
        errors = graph.get_errors()
        print(f"Analysis completed with {len(errors)} errors:")
        for error in errors:
            print(f"  {error}")

except CircularDependencyError as e:
    print(f"Circular dependency detected: {e.cycle}")
except ValidationError as e:
    print(f"Validation error: {e}")
except ConfigurationError as e:
    print(f"Configuration error: {e}")
except DependencyAnalyzerError as e:
    print(f"Analysis error: {e}")
```

## Testing

The project includes comprehensive unit tests and integration tests to ensure reliability and maintainability.

### Running Tests

```bash
# Using uv (recommended)
uv install --group test
uv run pytest

# Using pip
pip install -e ".[test]"
pytest

# Run with coverage
uv run pytest --cov=src --cov-report=html

# Run specific test categories
uv run pytest -m unit          # Unit tests only
uv run pytest -m integration   # Integration tests only
uv run pytest -m "not slow"    # Exclude slow tests
```

### Test Structure

- **Unit Tests**: Test individual components in isolation
- **Integration Tests**: Test complete workflows with real projects
- **Parser Tests**: Test file parsing capabilities for Vue and React
- **Formatter Tests**: Test output generation in various formats

### Coverage

The test suite maintains high coverage across all modules:
- **Overall coverage: 70.8%** (and growing)
- **Core modules: 85%+** (dependency_analyzer, dependency_graph)
- **Parsers: 85%+** (vue_parser, react_parser)
- **Configuration: 90%+** (config, validation, exceptions)
- **Metrics: 76%+** (advanced metrics and analysis)
- **New test suites**: 251 passing tests with comprehensive edge case coverage

For detailed testing information, see [docs/TESTING_GUIDE.md](docs/TESTING_GUIDE.md).

## API Documentation

### Programmatic Usage

```python
from src.diff_analyzer import (
    DependencyGraph,
    JSONFormatter,
    MermaidFormatter
)

# Analyze a project
graph = DependencyGraph("/path/to/project")
graph.analyze_project()

# Get comprehensive statistics
stats = graph.get_statistics()
print(f"Total files: {stats['total_files']}")
print(f"Circular dependencies: {stats['circular_dependencies']}")

# Find specific relationships
dependencies = graph.get_dependencies("/path/to/file.vue")
dependents = graph.get_dependents("/path/to/file.vue")

# Generate different output formats
json_formatter = JSONFormatter(graph)
json_output = json_formatter.format(include_details=True)

mermaid_formatter = MermaidFormatter(graph)
mermaid_diagram = mermaid_formatter.format(diagram_type='flowchart')
```

### Extension Points

The analyzer is designed to be extensible:

- **Custom Parsers**: Add support for new frameworks or file types
- **Custom Formatters**: Create new output formats for specific tools
- **Custom Analysis**: Extend graph analysis with domain-specific logic

For detailed API documentation, see [docs/API.md](docs/API.md).

For developer guidance and extension examples, see [docs/DEVELOPER_GUIDE.md](docs/DEVELOPER_GUIDE.md).

## Contributing

We welcome contributions! Please follow these guidelines:

1. **Fork the repository** and create a feature branch
2. **Add comprehensive tests** for new functionality
3. **Follow the existing code style** and architecture patterns
4. **Update documentation** for API changes
5. **Ensure all tests pass** with good coverage
6. **Submit a pull request** with a clear description

### Development Setup

```bash
# Clone the repository
git clone <repository-url>
cd diff_analyzer

# Install in development mode with all dependencies (using uv)
uv install --group test --group dev

# Or using pip
pip install -e ".[test,dev]"

# Install pre-commit hooks (if available)
pre-commit install

# Run tests to verify setup
uv run pytest
```

### Code Quality

The project uses several tools to maintain code quality:

- **Black**: Code formatting
- **isort**: Import sorting
- **flake8**: Linting
- **mypy**: Type checking
- **pytest**: Testing with coverage

Run quality checks:

```bash
# Using uv
uv run black src tests
uv run isort src tests
uv run flake8 src tests
uv run mypy src
uv run pytest --cov=src

# Or using pip installation
black src tests
isort src tests
flake8 src tests
mypy src
pytest --cov=src
```

## License

MIT License - see LICENSE file for details.

## Troubleshooting

### Common Issues

1. **"No files found"**: Check that your project contains supported file types (.vue, .tsx, .jsx, .ts, .js)
2. **"Dependencies not resolved"**: Ensure import paths are correct and files exist
3. **"Tree-sitter errors"**: Make sure tree-sitter-typescript is properly installed

### Debug Mode

Use `--verbose` flag to see detailed parsing information:

```bash
uv run python -m src.diff_analyzer.main ./project --verbose
```

## Recent Enhancements

### 🚀 Performance & Scalability
- **Multi-threaded processing** for large projects (3x faster)
- **Intelligent caching** system for improved performance
- **Memory optimization** with file size limits
- **Parallel analysis** with configurable worker threads

### 🛡️ Enterprise-Ready Features
- **Comprehensive error handling** with detailed reporting
- **Flexible configuration system** (JSON/YAML support)
- **Advanced metrics** and architectural analysis
- **Professional reporting** with recommendations

### 📊 Advanced Analysis
- **Complexity metrics** (cyclomatic complexity, coupling analysis)
- **Architectural insights** (layered architecture, MVC patterns)
- **Quality assessments** (code smells, maintainability scores)
- **Performance impact** analysis (bundle size, loading chains)

### 🔧 Integration & Workflows
- **Multiple output formats**: CSV, XML, SARIF, GitHub Actions
- **CI/CD integration** with annotations and summaries
- **Configuration-driven** analysis for different environments
- **Professional reports** in Markdown format

## Enhanced Output Formats

### New Formats Available
```bash
# Comprehensive metrics with recommendations
--format metrics-json

# Professional analysis reports
--format markdown-report

# Spreadsheet-compatible export
--format csv

# Enterprise XML format
--format xml

# CI/CD integration
--format github-actions

# Security tool integration
--format sarif
```

## Configuration System

Create a `.dependency-analyzer.json` file in your project:

```json
{
  "max_workers": 4,
  "parallel_processing": true,
  "exclude_patterns": ["node_modules", ".git", "dist", "coverage"],
  "analysis_rules": {
    "max_dependency_depth": 50,
    "warn_circular_deps": true,
    "warn_large_files": true
  },
  "output_options": {
    "include_line_numbers": true,
    "sort_dependencies": true
  }
}
```

## Advanced Usage Examples

### CI/CD Integration
```bash
# GitHub Actions workflow
uv run python -m src.diff_analyzer.main . \
  --format github-actions \
  --metrics \
  --recommendations \
  --output $GITHUB_STEP_SUMMARY

# Generate SARIF for security tools
uv run python -m src.diff_analyzer.main . \
  --format sarif \
  --output dependency-analysis.sarif
```

### Professional Reports
```bash
# Comprehensive analysis report
uv run python -m src.diff_analyzer.main . \
  --format markdown-report \
  --metrics \
  --recommendations \
  --output ARCHITECTURE_ANALYSIS.md

# Metrics export for tracking
uv run python -m src.diff_analyzer.main . \
  --format csv \
  --output metrics.csv
```

### Enterprise Integration
```bash
# XML format for enterprise tools
uv run python -m src.diff_analyzer.main . \
  --format xml \
  --config enterprise-config.json \
  --output dependencies.xml
```

## Roadmap

### Completed ✅
- [x] Performance optimizations for large projects
- [x] Advanced error handling and validation
- [x] Configuration system with multiple sources
- [x] Comprehensive metrics and analysis
- [x] CI/CD integration tools
- [x] Multiple output formats

### Planned 🚧
- [ ] Support for more frameworks (Angular, Svelte)
- [ ] Integration with popular bundlers (Webpack, Vite)
- [ ] Web-based visualization interface
- [ ] Incremental analysis and caching
- [ ] Real-time dependency monitoring