# API Documentation

## Overview

The Vue.js and React Dependency Analyzer provides a comprehensive API for analyzing dependency relationships in modern JavaScript/TypeScript projects. The API is designed to be extensible, performant, and supports multiple output formats with advanced metrics and analysis capabilities.

## Quick Start

```python
from diff_analyzer import DependencyGraph, Config, DependencyMetrics
from diff_analyzer.output_formatters import J<PERSON><PERSON>ormatter, MarkdownReportFormatter

# Load configuration
config = Config("/path/to/project")
config.load_config()

# Analyze project
graph = DependencyGraph("/path/to/project", config=config)
graph.analyze_project()

# Get metrics
metrics = DependencyMetrics(graph)
all_metrics = metrics.calculate_all_metrics()

# Generate reports
json_formatter = JSONFormatter(graph)
report = json_formatter.format(include_metrics=True)
```

## Core Classes

### DependencyGraph

The main class for analyzing project dependencies with advanced features.

```python
from diff_analyzer import DependencyGraph, Config

# Initialize with configuration
config = Config("/path/to/project")
config.load_config()

graph = DependencyGraph("/path/to/project", config=config)

# Analyze the project with parallel processing
graph.analyze_project()

# Get comprehensive statistics
stats = graph.get_statistics()
print(f"Total files: {stats['total_files']}")
print(f"Circular dependencies: {stats['circular_dependencies']}")

# Get advanced metrics
advanced_metrics = graph.get_advanced_metrics()
```

#### Methods

##### `__init__(project_root: str, config: Optional[Config] = None)`

Initialize the dependency graph analyzer with optional configuration.

**Parameters:**
- `project_root` (str): Absolute path to the project root directory
- `config` (Optional[Config]): Configuration object for analysis settings

**Example:**
```python
# Basic initialization
graph = DependencyGraph("/home/<USER>/my-vue-project")

# With custom configuration
config = Config("/home/<USER>/my-vue-project")
config.load_config("custom-config.json")
graph = DependencyGraph("/home/<USER>/my-vue-project", config=config)
```

##### `analyze_project(exclude_patterns: Optional[List[str]] = None) -> None`

Analyze the entire project and build the dependency graph.

**Parameters:**
- `exclude_patterns` (Optional[List[str]]): Patterns to exclude from analysis

**Default exclusions:** `['node_modules', '.git', 'dist', 'build', '.next', '.nuxt']`

**Example:**
```python
# Use default exclusions
graph.analyze_project()

# Custom exclusions
graph.analyze_project(exclude_patterns=['node_modules', 'coverage', 'docs'])
```

##### `get_statistics() -> Dict[str, int]`

Get comprehensive project statistics.

**Returns:**
- Dictionary containing:
  - `total_files`: Number of analyzed files
  - `total_dependencies`: Number of local dependencies
  - `entry_points`: Number of entry point files
  - `leaf_nodes`: Number of leaf node files
  - `circular_dependencies`: Number of circular dependency cycles
  - `external_packages`: Number of external dependencies

**Example:**
```python
stats = graph.get_statistics()
print(f"Project has {stats['total_files']} files")
print(f"Found {stats['circular_dependencies']} circular dependencies")
```

##### `find_circular_dependencies() -> List[List[str]]`

Find all circular dependencies in the project.

**Returns:**
- List of cycles, where each cycle is a list of file paths

**Example:**
```python
cycles = graph.find_circular_dependencies()
for cycle in cycles:
    print(f"Circular dependency: {' -> '.join(cycle)}")
```

##### `get_dependencies(file_path: str) -> List[str]`

Get all direct dependencies of a specific file.

**Parameters:**
- `file_path` (str): Absolute path to the file

**Returns:**
- List of file paths that the given file depends on

**Example:**
```python
deps = graph.get_dependencies("/project/src/main.ts")
print(f"main.ts depends on: {deps}")
```

##### `get_dependents(file_path: str) -> List[str]`

Get all files that depend on a specific file.

**Parameters:**
- `file_path` (str): Absolute path to the file

**Returns:**
- List of file paths that depend on the given file

**Example:**
```python
dependents = graph.get_dependents("/project/src/components/Button.vue")
print(f"Files that use Button.vue: {dependents}")
```

##### `find_dependency_path(source: str, target: str) -> Optional[List[str]]`

Find the dependency path between two files.

**Parameters:**
- `source` (str): Source file path
- `target` (str): Target file path

**Returns:**
- List of file paths representing the dependency path, or None if no path exists

**Example:**
```python
path = graph.find_dependency_path("/project/src/main.ts", "/project/src/utils/helper.ts")
if path:
    print(f"Dependency path: {' -> '.join(path)}")
```

### Output Formatters

#### JSONFormatter

Format dependency analysis results as structured JSON.

```python
from src.diff_analyzer import JSONFormatter

formatter = JSONFormatter(dependency_graph)
json_output = formatter.format(include_details=True)

# Parse the JSON
import json
data = json.loads(json_output)
```

##### `format(include_details: bool = True) -> str`

Generate JSON output.

**Parameters:**
- `include_details` (bool): Include detailed import/export information

**Returns:**
- JSON string with complete dependency analysis

#### DOTFormatter

Format dependency graph as DOT format for Graphviz.

```python
from src.diff_analyzer import DOTFormatter

formatter = DOTFormatter(dependency_graph)
dot_output = formatter.format(include_external=False, cluster_by_directory=True)

# Save to file for Graphviz processing
with open("dependencies.dot", "w") as f:
    f.write(dot_output)
```

##### `format(include_external: bool = False, cluster_by_directory: bool = True) -> str`

Generate DOT format output.

**Parameters:**
- `include_external` (bool): Include external dependencies in the graph
- `cluster_by_directory` (bool): Group files by directory

**Returns:**
- DOT format string for Graphviz

#### MermaidFormatter

Format dependency graph as Mermaid diagram syntax.

```python
from src.diff_analyzer import MermaidFormatter

formatter = MermaidFormatter(dependency_graph)
mermaid_output = formatter.format(diagram_type='flowchart')
```

##### `format(diagram_type: str = 'flowchart') -> str`

Generate Mermaid diagram syntax.

**Parameters:**
- `diagram_type` (str): Type of diagram ('flowchart' or 'graph')

**Returns:**
- Mermaid diagram syntax string

#### TextTreeFormatter

Format dependency graph as human-readable text tree.

```python
from src.diff_analyzer import TextTreeFormatter

formatter = TextTreeFormatter(dependency_graph)
text_output = formatter.format(show_details=True)
print(text_output)
```

##### `format(show_details: bool = False) -> str`

Generate text tree output.

**Parameters:**
- `show_details` (bool): Include detailed dependency information

**Returns:**
- Human-readable text tree representation

## Data Classes

### ImportInfo

Represents information about a single import statement.

```python
from src.diff_analyzer import ImportInfo, ImportType, DependencyType

import_info = ImportInfo(
    source_file="/project/src/main.ts",
    imported_module="vue",
    import_type=ImportType.NAMED,
    dependency_type=DependencyType.EXTERNAL,
    imported_names=["createApp"],
    line_number=1,
    is_type_only=False
)
```

**Attributes:**
- `source_file` (str): File containing the import
- `imported_module` (str): Module being imported
- `import_type` (ImportType): Type of import syntax
- `dependency_type` (DependencyType): Category of dependency
- `imported_names` (List[str]): Imported identifiers
- `line_number` (int): Line number of import
- `is_type_only` (bool): TypeScript type-only import

### DependencyNode

Represents a file node in the dependency graph.

```python
from src.diff_analyzer import DependencyNode

node = DependencyNode(
    file_path="/project/src/App.vue",
    imports=[...],  # List of ImportInfo objects
    exports=["App"],
    is_entry_point=False,
    is_leaf_node=True
)
```

**Attributes:**
- `file_path` (str): Absolute path to the file
- `imports` (List[ImportInfo]): All imports in the file
- `exports` (List[str]): Exported identifiers
- `is_entry_point` (bool): Whether file is an entry point
- `is_leaf_node` (bool): Whether file has no dependencies

### Config

Configuration management class for flexible project settings.

```python
from diff_analyzer import Config

# Load configuration from project directory
config = Config("/path/to/project")
config.load_config()

# Load with explicit config file
config.load_config("/path/to/custom-config.json")

# Access configuration values
max_workers = config.get("max_workers", 4)
exclude_patterns = config.get("exclude_patterns", [])

# Set configuration values
config.set("verbose", True)
config.set("max_workers", 8)
```

#### Methods

##### `load_config(config_file: Optional[str] = None) -> Dict[str, Any]`

Load configuration from multiple sources with precedence.

**Parameters:**
- `config_file` (Optional[str]): Explicit config file path

**Returns:**
- Dictionary containing merged configuration

**Configuration Sources (in order of precedence):**
1. Explicit config file parameter
2. Environment variables
3. Project config file (.dependency-analyzer.json/yaml)
4. User config file (~/.dependency-analyzer.json/yaml)
5. Default values

### DependencyMetrics

Advanced metrics and analysis for dependency graphs.

```python
from diff_analyzer import DependencyMetrics

# Initialize with dependency graph
metrics = DependencyMetrics(graph)

# Calculate all metrics
all_metrics = metrics.calculate_all_metrics()

# Access specific metric categories
basic = metrics.get_basic_metrics()
complexity = metrics.get_complexity_metrics()
quality = metrics.get_quality_metrics()
performance = metrics.get_performance_metrics()
```

#### Methods

##### `calculate_all_metrics() -> Dict[str, Any]`

Calculate comprehensive metrics for the project.

**Returns:**
- Dictionary containing all metric categories:
  - `basic_metrics`: File counts, dependency counts
  - `complexity_metrics`: Cyclomatic complexity, coupling analysis
  - `architectural_metrics`: Component analysis, modularity
  - `maintainability_metrics`: Change impact, instability
  - `quality_metrics`: Code smells, best practices
  - `performance_metrics`: Bundle analysis, optimization opportunities

##### `get_basic_metrics() -> Dict[str, Any]`

Get fundamental project statistics.

**Returns:**
- `total_files`: Number of analyzed files
- `total_dependencies`: Number of dependency relationships
- `average_dependencies_per_file`: Mean dependencies per file
- `entry_points`: Number of entry point files
- `leaf_nodes`: Number of leaf node files
- `isolated_files`: Number of files with no connections

## Advanced Formatters

### MetricsJSONFormatter

Enhanced JSON formatter with metrics support.

```python
from diff_analyzer.advanced_formatters import MetricsJSONFormatter

formatter = MetricsJSONFormatter(graph)

# Basic JSON output
json_output = formatter.format()

# Include advanced metrics
json_with_metrics = formatter.format(include_metrics=True)

# Include file-level analysis
detailed_json = formatter.format(
    include_metrics=True,
    include_file_analysis=True
)
```

### MarkdownReportFormatter

Comprehensive markdown reports with analysis and recommendations.

```python
from diff_analyzer.advanced_formatters import MarkdownReportFormatter

formatter = MarkdownReportFormatter(graph)

# Basic report
report = formatter.format()

# Include metrics and recommendations
comprehensive_report = formatter.format(
    include_metrics=True,
    include_recommendations=True
)
```

## Integration Formatters

### CSVFormatter

Export data in CSV format for spreadsheet analysis.

```python
from diff_analyzer.integration_formatters import CSVFormatter

formatter = CSVFormatter(graph)

# Export dependencies
dependencies_csv = formatter.format("dependencies")

# Export file information
files_csv = formatter.format("files")

# Export metrics
metrics_csv = formatter.format("metrics")
```

### SARIFFormatter

Static Analysis Results Interchange Format for CI/CD integration.

```python
from diff_analyzer.integration_formatters import SARIFFormatter

formatter = SARIFFormatter(graph)
sarif_output = formatter.format()

# Save for CI/CD tools
with open("analysis.sarif", "w") as f:
    f.write(sarif_output)
```

## Enumerations

### ImportType

Types of import statements:
- `DEFAULT`: Default imports (`import App from './App.vue'`)
- `NAMED`: Named imports (`import { createApp } from 'vue'`)
- `NAMESPACE`: Namespace imports (`import * as utils from './utils'`)
- `DYNAMIC`: Dynamic imports (`import('./component')`)
- `REQUIRE`: CommonJS require (`const fs = require('fs')`)

### DependencyType

Categories of dependencies:
- `LOCAL`: Project-relative imports (`./component`, `../utils`)
- `EXTERNAL`: Third-party packages (`vue`, `react`, `lodash`)
- `BUILTIN`: Node.js built-in modules (`fs`, `path`, `crypto`)

## Error Handling

The API handles various error conditions gracefully:

```python
from src.diff_analyzer import DependencyGraph

try:
    graph = DependencyGraph("/path/to/project")
    graph.analyze_project()
except Exception as e:
    print(f"Analysis failed: {e}")

# Individual file parsing errors are logged but don't stop analysis
# Check the console output for parsing warnings
```

## Performance Considerations

- Large projects (>1000 files) may take several seconds to analyze
- Exclude unnecessary directories to improve performance
- Use `include_details=False` in JSON formatter for faster output
- Consider using `--exclude` patterns for build artifacts and dependencies
