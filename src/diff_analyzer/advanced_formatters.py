"""
Advanced output formatters with metrics and analysis.

This module provides enhanced output formatters that include comprehensive
metrics, analysis insights, and recommendations for improving code quality
and maintainability.
"""

import json
from pathlib import Path
from typing import Any, Dict

from .output_formatters import OutputFormatter


class MetricsJSONFormatter(OutputFormatter):
    """
    JSON formatter that includes comprehensive metrics and analysis.

    Extends the basic JSON formatter to include advanced metrics,
    architectural insights, and quality assessments.
    """

    def format(self, include_metrics: bool = True, include_file_analysis: bool = False) -> str:
        """
        Format dependency graph with comprehensive metrics.

        Args:
            include_metrics (bool): Whether to include advanced metrics
            include_file_analysis (bool): Whether to include per-file analysis

        Returns:
            str: JSON formatted output with metrics
        """
        # Get basic dependency information
        result = {
            "project_root": str(self.graph.project_root),
            "analysis_timestamp": self._get_timestamp(),
            "configuration": self._get_config_info(),
            "basic_statistics": self.graph.get_statistics(),
            "files": self._get_files_info(),
            "dependencies": self._get_dependencies_info(),
        }

        # Add advanced metrics if requested
        if include_metrics:
            result["advanced_metrics"] = self.graph.get_advanced_metrics()
            result["recommendations"] = self._generate_recommendations()

        # Add per-file analysis if requested
        if include_file_analysis:
            result["file_analysis"] = self._get_detailed_file_analysis()

        # Add error information
        if self.graph.has_errors():
            result["errors"] = {
                "error_count": len(self.graph.get_errors()),
                "error_summary": self.graph.get_error_summary(),
                "errors": [
                    str(error) for error in self.graph.get_errors()[:10]
                ],  # Limit to first 10
            }

        return json.dumps(result, indent=2, sort_keys=True)

    def _get_timestamp(self) -> str:
        """Get current timestamp."""
        from datetime import datetime

        return datetime.now().isoformat()

    def _get_config_info(self) -> Dict[str, Any]:
        """
        Get comprehensive configuration information for the analysis.

        Returns:
            Dict[str, Any]: Configuration details including:
                - config_sources: List of configuration sources used
                - max_workers: Number of worker threads
                - supported_extensions: File extensions analyzed
                - project_info: Project structure information
        """
        return {
            "config_sources": self.graph.config.get_config_sources(),
            "max_workers": self.graph.max_workers,
            "supported_extensions": list(self.graph.supported_extensions),
            "project_info": self.graph.project_info,
        }

    def _get_files_info(self) -> Dict[str, Dict[str, Any]]:
        """Get detailed file information."""
        files_info = {}

        for file_path, node in self.graph.nodes.items():
            rel_path = self._get_relative_path(file_path)
            files_info[rel_path] = {
                "absolute_path": file_path,
                "file_type": Path(file_path).suffix,
                "is_entry_point": node.is_entry_point,
                "is_leaf_node": node.is_leaf_node,
                "import_count": len(node.imports),
                "export_count": len(node.exports),
                "local_dependencies": len(
                    [imp for imp in node.imports if imp.dependency_type.name == "LOCAL"]
                ),
                "external_dependencies": len(
                    [imp for imp in node.imports if imp.dependency_type.name == "EXTERNAL"]
                ),
                "imports": [self._format_import_info(imp) for imp in node.imports],
                "exports": node.exports,
            }

        return files_info

    def _get_dependencies_info(self) -> Dict[str, Any]:
        """Get dependency relationship information."""
        dependencies = {}

        for source, target, data in self.graph.graph.edges(data=True):
            source_rel = self._get_relative_path(source)
            target_rel = self._get_relative_path(target)

            if source_rel not in dependencies:
                dependencies[source_rel] = []

            dependencies[source_rel].append(
                {
                    "target": target_rel,
                    "import_type": data.get("import_type", "unknown"),
                    "imported_names": data.get("imported_names", []),
                    "line_number": data.get("line_number"),
                }
            )

        return dependencies

    def _format_import_info(self, import_info) -> Dict[str, Any]:
        """Format import information."""
        return {
            "module": import_info.imported_module,
            "type": import_info.import_type.name,
            "dependency_type": import_info.dependency_type.name,
            "imported_names": import_info.imported_names,
            "line_number": import_info.line_number,
        }

    def _generate_recommendations(self) -> Dict[str, Any]:
        """Generate recommendations based on analysis."""
        metrics = self.graph.get_advanced_metrics()
        recommendations = {
            "high_priority": [],
            "medium_priority": [],
            "low_priority": [],
            "optimizations": [],
        }

        # Analyze metrics and generate recommendations
        complexity_metrics = metrics.get("complexity_metrics", {})
        quality_metrics = metrics.get("quality_metrics", {})

        # High priority recommendations
        if complexity_metrics.get("max_fan_out", 0) > 10:
            recommendations["high_priority"].append(
                {
                    "type": "high_coupling",
                    "description": "Some files have very high coupling (>10 dependencies)",
                    "action": "Consider breaking down large files and reducing dependencies",
                    "files": [
                        f
                        for f, degree in [
                            (f, self.graph.graph.out_degree(f)) for f in self.graph.graph.nodes()
                        ]
                        if degree > 10
                    ][:5],
                }
            )

        circular_deps = quality_metrics.get("dependency_health", {}).get("circular_dependencies", 0)
        if circular_deps > 0:
            recommendations["high_priority"].append(
                {
                    "type": "circular_dependencies",
                    "description": f"Found {circular_deps} circular dependencies",
                    "action": "Resolve circular dependencies to improve maintainability",
                    "impact": "high",
                }
            )

        # Medium priority recommendations
        code_smells = quality_metrics.get("code_smells", [])
        if len(code_smells) > 5:
            recommendations["medium_priority"].append(
                {
                    "type": "code_smells",
                    "description": f"Found {len(code_smells)} potential code smells",
                    "action": "Review and refactor files with code smells",
                    "examples": code_smells[:3],
                }
            )

        # Performance optimizations
        performance_metrics = metrics.get("performance_metrics", {})
        lazy_loading_opportunities = performance_metrics.get("lazy_loading_opportunities", [])
        if lazy_loading_opportunities:
            recommendations["optimizations"].append(
                {
                    "type": "lazy_loading",
                    "description": (
                        f"Found {len(lazy_loading_opportunities)} lazy loading opportunities"
                    ),
                    "action": "Consider implementing lazy loading for these components",
                    "files": lazy_loading_opportunities[:5],
                }
            )

        return recommendations

    def _get_detailed_file_analysis(self) -> Dict[str, Dict[str, Any]]:
        """Get detailed analysis for each file."""
        file_analysis = {}

        for file_path in self.graph.nodes.keys():
            rel_path = self._get_relative_path(file_path)
            analysis = self.graph.get_file_analysis(file_path)
            if analysis:
                file_analysis[rel_path] = analysis

        return file_analysis


class MarkdownReportFormatter(OutputFormatter):
    """
    Markdown formatter for comprehensive analysis reports.

    Generates detailed markdown reports suitable for documentation
    and code review processes.
    """

    def format(self, include_metrics: bool = True, include_recommendations: bool = True) -> str:
        """
        Format dependency analysis as a comprehensive markdown report.

        Args:
            include_metrics (bool): Whether to include detailed metrics
            include_recommendations (bool): Whether to include recommendations

        Returns:
            str: Markdown formatted report
        """
        report_sections = []

        # Header
        report_sections.append(self._generate_header())

        # Executive Summary
        report_sections.append(self._generate_executive_summary())

        # Basic Statistics
        report_sections.append(self._generate_basic_statistics())

        # Metrics Analysis
        if include_metrics:
            report_sections.append(self._generate_metrics_analysis())

        # Recommendations
        if include_recommendations:
            report_sections.append(self._generate_recommendations_section())

        # File Analysis
        report_sections.append(self._generate_file_analysis())

        # Appendix
        report_sections.append(self._generate_appendix())

        return "\n\n".join(report_sections)

    def _generate_header(self) -> str:
        """Generate report header."""
        from datetime import datetime

        return f"""# Dependency Analysis Report

**Project:** {self.graph.project_root.name}
**Analysis Date:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
**Tool:** Vue.js and React Dependency Analyzer v{self._get_version()}

---"""

    def _generate_executive_summary(self) -> str:
        """Generate executive summary."""
        stats = self.graph.get_statistics()
        metrics = self.graph.get_advanced_metrics()

        complexity_score = metrics.get("quality_metrics", {}).get("best_practices_score", 0)
        health_score = (
            metrics.get("quality_metrics", {})
            .get("dependency_health", {})
            .get("dependency_health_score", 0)
        )

        return f"""## Executive Summary

This report provides a comprehensive analysis of the project's dependency structure and
code quality.

### Key Findings

- **Total Files Analyzed:** {stats['total_files']}
- **Total Dependencies:** {stats['total_dependencies']}
- **Circular Dependencies:** {stats.get('circular_dependencies', 0)}
- **Code Quality Score:** {complexity_score:.1f}/100
- **Dependency Health Score:** {health_score:.1f}/100

### Overall Assessment

{self._get_overall_assessment(complexity_score, health_score)}"""

    def _generate_basic_statistics(self) -> str:
        """Generate basic statistics section."""
        stats = self.graph.get_statistics()

        return f"""## Project Statistics

| Metric | Value |
|--------|-------|
| Total Files | {stats['total_files']} |
| Total Dependencies | {stats['total_dependencies']} |
| Entry Points | {stats['entry_points']} |
| Leaf Nodes | {stats['leaf_nodes']} |
| Average Dependencies per File | {stats['total_dependencies'] / max(stats['total_files'], 1):.1f} |
| Circular Dependencies | {stats.get('circular_dependencies', 0)} |"""

    def _generate_metrics_analysis(self) -> str:
        """Generate detailed metrics analysis."""
        metrics = self.graph.get_advanced_metrics()

        sections = []

        # Complexity Analysis
        complexity = metrics.get("complexity_metrics", {})
        sections.append(
            f"""### Complexity Analysis

- **Cyclomatic Complexity:** {complexity.get('cyclomatic_complexity', 0)}
- **Maximum Dependency Depth:** {complexity.get('max_dependency_depth', 0)}
- **Maximum Fan-out:** {complexity.get('max_fan_out', 0)}
- **Average Fan-in:** {complexity.get('average_fan_in', 0):.1f}"""
        )

        # Architectural Analysis
        architectural = metrics.get("architectural_metrics", {})
        sections.append(
            f"""### Architectural Analysis

- **Modularity Score:** {architectural.get('modularity_score', 0):.2f}
- **Connected Components:** {architectural.get('weakly_connected_components', 0)}
- **Largest Component Size:** {architectural.get('largest_component_size', 0)}"""
        )

        return "## Detailed Metrics\n\n" + "\n\n".join(sections)

    def _generate_recommendations_section(self) -> str:
        """Generate recommendations section."""
        formatter = MetricsJSONFormatter(self.graph)
        recommendations = formatter._generate_recommendations()

        sections = []

        if recommendations["high_priority"]:
            sections.append("### High Priority Issues\n")
            for rec in recommendations["high_priority"]:
                sections.append(
                    f"- **{rec['type'].replace('_', ' ').title()}:** {rec['description']}"
                )
                sections.append(f"  - *Action:* {rec['action']}")

        if recommendations["medium_priority"]:
            sections.append("### Medium Priority Issues\n")
            for rec in recommendations["medium_priority"]:
                sections.append(
                    f"- **{rec['type'].replace('_', ' ').title()}:** {rec['description']}"
                )
                sections.append(f"  - *Action:* {rec['action']}")

        if recommendations["optimizations"]:
            sections.append("### Optimization Opportunities\n")
            for rec in recommendations["optimizations"]:
                sections.append(
                    f"- **{rec['type'].replace('_', ' ').title()}:** {rec['description']}"
                )
                sections.append(f"  - *Action:* {rec['action']}")

        return (
            "## Recommendations\n\n" + "\n\n".join(sections)
            if sections
            else "## Recommendations\n\nNo specific recommendations at this time."
        )

    def _generate_file_analysis(self) -> str:
        """Generate file analysis section."""
        # Get top files by various metrics
        metrics = self.graph.get_advanced_metrics()
        maintainability = metrics.get("maintainability_metrics", {})

        sections = []

        # Most complex files
        if "most_central_files" in metrics.get("architectural_metrics", {}):
            central_files = metrics["architectural_metrics"]["most_central_files"]
            if central_files:
                sections.append("### Most Central Files\n")
                for file_path, score in central_files:
                    rel_path = self._get_relative_path(file_path)
                    sections.append(f"- `{rel_path}` (score: {score:.2f})")

        # High coupling files
        if "high_coupling_files" in maintainability:
            coupling_files = maintainability["high_coupling_files"]
            if coupling_files:
                sections.append("### High Coupling Files\n")
                for file_path, coupling in coupling_files[:5]:
                    rel_path = self._get_relative_path(file_path)
                    sections.append(f"- `{rel_path}` (coupling: {coupling})")

        return (
            "## File Analysis\n\n" + "\n\n".join(sections)
            if sections
            else "## File Analysis\n\nNo significant files to highlight."
        )

    def _generate_appendix(self) -> str:
        """Generate appendix with additional information."""
        config_info = {
            "sources": self.graph.config.get_config_sources(),
            "extensions": list(self.graph.supported_extensions),
            "max_workers": self.graph.max_workers,
        }

        return f"""## Appendix

### Configuration
- **Config Sources:** {', '.join(config_info['sources'])}
- **Supported Extensions:** {', '.join(config_info['extensions'])}
- **Max Workers:** {config_info['max_workers']}

### Analysis Details
- **Project Type:** {self.graph.project_info.get('framework_type', 'unknown')}
- **Estimated Size:** {self.graph.project_info.get('estimated_size', 'unknown')}
- **Has TypeScript:** {self.graph.project_info.get('has_tsconfig', False)}"""

    def _get_version(self) -> str:
        """Get tool version."""
        try:
            from . import __version__

            return __version__
        except (ImportError, AttributeError):
            return "unknown"

    def _get_overall_assessment(self, complexity_score: float, health_score: float) -> str:
        """Get overall assessment based on scores."""
        avg_score = (complexity_score + health_score) / 2

        if avg_score >= 80:
            return "The project demonstrates good code quality and maintainable architecture."
        elif avg_score >= 60:
            return "The project has moderate code quality with some areas for improvement."
        elif avg_score >= 40:
            return "The project shows several quality issues that should be addressed."
        else:
            return (
                "The project has significant quality and maintainability concerns that "
                "require immediate attention."
            )
