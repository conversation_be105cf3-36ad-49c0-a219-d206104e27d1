"""
Custom exceptions for the dependency analyzer.

This module defines specific exception types for different error conditions
that can occur during dependency analysis, providing better error handling
and debugging capabilities.
"""

from typing import List, Optional


class DependencyAnalyzerError(Exception):
    """Base exception for all dependency analyzer errors."""

    def __init__(
        self, message: str, file_path: Optional[str] = None, line_number: Optional[int] = None
    ):
        """
        Initialize the exception.

        Args:
            message (str): Error message
            file_path (Optional[str]): File path where the error occurred
            line_number (Optional[int]): Line number where the error occurred
        """
        self.message = message
        self.file_path = file_path
        self.line_number = line_number

        # Build full error message
        full_message = message
        if file_path:
            full_message = f"{message} in file: {file_path}"
        if line_number:
            full_message = f"{full_message} at line {line_number}"

        super().__init__(full_message)


class ParseError(DependencyAnalyzerError):
    """Exception raised when file parsing fails."""

    pass


class ImportResolutionError(DependencyAnalyzerError):
    """Exception raised when import path resolution fails."""

    def __init__(
        self,
        message: str,
        import_path: str,
        file_path: Optional[str] = None,
        line_number: Optional[int] = None,
    ):
        """
        Initialize the import resolution error.

        Args:
            message (str): Error message
            import_path (str): The import path that failed to resolve
            file_path (Optional[str]): File path where the error occurred
            line_number (Optional[int]): Line number where the error occurred
        """
        self.import_path = import_path
        super().__init__(f"{message}: '{import_path}'", file_path, line_number)


class CircularDependencyError(DependencyAnalyzerError):
    """Exception raised when circular dependencies are detected."""

    def __init__(self, cycle: List[str]):
        """
        Initialize the circular dependency error.

        Args:
            cycle (List[str]): List of file paths forming the circular dependency
        """
        self.cycle = cycle
        cycle_str = " -> ".join(cycle)
        super().__init__(f"Circular dependency detected: {cycle_str}")


class FileAccessError(DependencyAnalyzerError):
    """Exception raised when file access fails."""

    pass


class ConfigurationError(DependencyAnalyzerError):
    """Exception raised when configuration is invalid."""

    pass


class GraphAnalysisError(DependencyAnalyzerError):
    """Exception raised when graph analysis fails."""

    pass


class OutputFormattingError(DependencyAnalyzerError):
    """Exception raised when output formatting fails."""

    def __init__(self, message: str, format_type: str):
        """
        Initialize the output formatting error.

        Args:
            message (str): Error message
            format_type (str): The output format that failed
        """
        self.format_type = format_type
        super().__init__(f"{message} for format: {format_type}")


class ValidationError(DependencyAnalyzerError):
    """Exception raised when input validation fails."""

    pass


class TreeSitterError(DependencyAnalyzerError):
    """Exception raised when tree-sitter parsing fails."""

    pass


class ProjectStructureError(DependencyAnalyzerError):
    """Exception raised when project structure is invalid or unexpected."""

    pass
