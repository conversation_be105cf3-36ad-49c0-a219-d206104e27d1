"""
Vue.js File Parser

Specialized parser for Vue Single File Components (.vue files).
This module provides comprehensive parsing capabilities for Vue SFC files,
extracting script sections, template component usage, and analyzing
Vue-specific dependency patterns.

The parser handles:
- Multiple script sections (script and script setup)
- Different script languages (JavaScript, TypeScript)
- Vue component registrations and imports
- Template component usage analysis
- Vue-specific metadata extraction
"""

import re
from pathlib import Path
from typing import Dict, List, Optional

from tree_sitter import Parser

from .dependency_analyzer import BaseParser, DependencyNode, ImportInfo, ImportType


class VueParser(BaseParser):
    """
    Specialized parser for Vue.js Single File Components.

    This parser extends BaseParser to handle Vue-specific syntax and patterns,
    including Single File Component structure, Vue component registrations,
    and template analysis.

    Attributes:
        vue_component_patterns (List[str]): Regex patterns for detecting Vue component registrations

    Examples:
        >>> parser = VueParser()
        >>> node = parser.parse_file("/path/to/Component.vue")
        >>> print(node.exports)  # ['Component']
        >>> print(len(node.imports))  # Number of imports found
    """

    def __init__(self):
        """
        Initialize the Vue parser with Vue-specific patterns.

        Sets up regex patterns for detecting Vue component registrations
        and inherits tree-sitter parsers from BaseParser.
        """
        super().__init__()
        self.vue_component_patterns = [
            r"components\s*:\s*\{([^}]+)\}",  # components: { ... }
            r'component\s*\(\s*[\'"]([^\'"]+)[\'"]',  # Vue.component('name', ...)
        ]

    def parse_file(self, file_path: str) -> Optional[DependencyNode]:
        """
        Parse a Vue Single File Component and extract dependency information.

        Processes a .vue file by extracting script sections, analyzing imports
        and exports, detecting Vue component registrations, and examining
        template component usage.

        Args:
            file_path (str): Absolute path to the Vue file to parse

        Returns:
            Optional[DependencyNode]: Parsed dependency information or None if parsing fails

        Examples:
            >>> parser = VueParser()
            >>> node = parser.parse_file("/src/components/UserProfile.vue")
            >>> if node:
            ...     print(f"Found {len(node.imports)} imports")
            ...     print(f"Exports: {node.exports}")
        """
        try:
            content = self._read_file_content(file_path)

            # Extract script sections from Vue SFC
            script_sections = self._extract_script_sections(content)

            all_imports = []
            all_exports = []

            for script_content, script_lang in script_sections:
                # Parse the script content using tree-sitter
                parser = self._get_parser_for_script(script_lang)
                tree = parser.parse(script_content.encode("utf-8"))

                # Extract imports from the script
                imports = self._extract_imports_from_tree(tree, script_content, file_path)
                all_imports.extend(imports)

                # Extract Vue-specific component registrations
                vue_imports = self._extract_vue_component_imports(script_content, file_path)
                all_imports.extend(vue_imports)

                # Extract exports
                exports = self._extract_exports_from_tree(tree, script_content)
                all_exports.extend(exports)

            # Also check template section for component usage
            template_imports = self._extract_template_component_usage(content, file_path)
            all_imports.extend(template_imports)

            return DependencyNode(
                file_path=file_path,
                imports=all_imports,
                exports=all_exports,
                is_entry_point=self._is_entry_point(file_path),
                is_leaf_node=len(all_imports) == 0,
            )

        except Exception as e:
            print(f"Error parsing Vue file {file_path}: {e}")
            return None

    def _extract_script_sections(self, content: str) -> List[tuple]:
        """Extract all script sections from Vue SFC."""
        script_sections = []

        # Pattern to match script tags with optional lang attribute anywhere in the tag
        script_pattern = r"<script([^>]*)>(.*?)</script>"

        matches = re.findall(script_pattern, content, re.DOTALL | re.IGNORECASE)

        for attributes, script_content in matches:
            # Extract lang attribute from the attributes string
            lang_match = re.search(r'lang=[\'"]([^\'"]*)[\'"]', attributes)
            lang = lang_match.group(1) if lang_match else "js"

            # Clean up the script content
            script_content = script_content.strip()
            if script_content:
                script_sections.append((script_content, lang))

        return script_sections

    def _get_parser_for_script(self, script_lang: str) -> Parser:
        """Get the appropriate parser based on script language."""
        if script_lang.lower() in ["ts", "typescript"]:
            return self.ts_parser
        elif script_lang.lower() in ["tsx", "jsx"]:
            return self.tsx_parser
        else:
            # Default to TypeScript parser for JavaScript as well
            return self.ts_parser

    def _extract_vue_component_imports(
        self, script_content: str, source_file: str
    ) -> List[ImportInfo]:
        """Extract Vue-specific component registrations and imports."""
        imports = []

        # Strategy 1: Look for components object in Vue component definition
        # This handles the most common pattern:
        # components: { UserCard, ContactInfo, 'custom-name': CustomComponent }
        components_pattern = r"components\s*:\s*\{([^}]+)\}"
        matches = re.findall(components_pattern, script_content, re.DOTALL)

        for match in matches:
            # Extract component names from the components object
            # This regex finds both simple names (UserCard) and quoted names ('custom-name')
            component_names = re.findall(r"(\w+)", match)

            for component_name in component_names:
                # Try to find corresponding import statement for this component
                # This links component registration to its import statement
                import_pattern = rf'import\s+{component_name}\s+from\s+[\'"]([^\'"]+)[\'"]'
                import_match = re.search(import_pattern, script_content)

                if import_match:
                    import_path = import_match.group(1)
                    # Resolve the import path to handle relative paths and aliases
                    resolved_path, dep_type = self._resolve_import_path(
                        f"'{import_path}'", source_file
                    )

                    # Create ImportInfo for the component dependency
                    imports.append(
                        ImportInfo(
                            source_file=source_file,
                            imported_module=resolved_path,
                            import_type=ImportType.DEFAULT,
                            dependency_type=dep_type,
                            imported_names=[component_name],
                            line_number=self._get_line_number(script_content, import_match.start()),
                        )
                    )

        # Strategy 2: Look for Vue.component() global registrations
        # This handles Vue 2 style global component registrations:
        # Vue.component('my-component', MyComponent)
        global_component_pattern = r'Vue\.component\s*\(\s*[\'"]([^\'"]+)[\'"]\s*,\s*(\w+)\s*\)'
        matches = re.findall(global_component_pattern, script_content)

        for component_name, component_var in matches:
            # Find the import for this component variable
            import_pattern = rf'import\s+{component_var}\s+from\s+[\'"]([^\'"]+)[\'"]'
            import_match = re.search(import_pattern, script_content)

            if import_match:
                import_path = import_match.group(1)
                resolved_path, dep_type = self._resolve_import_path(f"'{import_path}'", source_file)

                imports.append(
                    ImportInfo(
                        source_file=source_file,
                        imported_module=resolved_path,
                        import_type=ImportType.DEFAULT,
                        dependency_type=dep_type,
                        imported_names=[component_var],
                        line_number=self._get_line_number(script_content, import_match.start()),
                    )
                )

        return imports

    def _extract_template_component_usage(self, content: str, source_file: str) -> List[ImportInfo]:
        """Extract component usage from Vue template section."""
        imports = []

        # Extract template section
        template_pattern = r"<template[^>]*>(.*?)</template>"
        template_match = re.search(template_pattern, content, re.DOTALL | re.IGNORECASE)

        if not template_match:
            return imports

        template_content = template_match.group(1)

        # Find custom component tags (PascalCase or kebab-case)
        component_pattern = r"<([A-Z][a-zA-Z0-9]*|[a-z]+-[a-z-]+)"
        component_matches = re.findall(component_pattern, template_content)

        # Convert kebab-case to PascalCase for matching with imports
        used_components = set()
        for component in component_matches:
            if "-" in component:
                # Convert kebab-case to PascalCase
                pascal_case = "".join(word.capitalize() for word in component.split("-"))
                used_components.add(pascal_case)
            else:
                used_components.add(component)

        # These are implicit dependencies - components used in template
        # but we can't directly resolve their import paths from template alone
        # This information could be used for validation or warnings

        return imports

    def _extract_exports_from_tree(self, tree, script_content: str) -> List[str]:
        """Extract export statements from the script."""
        exports = []

        # Walk the tree to find export statements
        self._walk_tree_for_exports(tree.root_node, script_content, exports)

        return exports

    def _walk_tree_for_exports(self, node, script_content: str, exports: List[str]):
        """Walk the tree recursively to find export statements."""
        if node.type in ["export_statement", "export_assignment"]:
            export_text = script_content[node.start_byte : node.end_byte]
            exports.append(export_text)

        # Recursively process children
        for child in node.children:
            self._walk_tree_for_exports(child, script_content, exports)

    def _get_line_number(self, content: str, position: int) -> int:
        """Get line number for a given character position in content."""
        return content[:position].count("\n") + 1

    def _is_entry_point(self, file_path: str) -> bool:
        """Determine if this Vue file is likely an entry point."""
        path = Path(file_path)

        # Common entry point patterns for Vue apps
        entry_patterns = ["main.vue", "app.vue", "index.vue", "router.vue"]

        return (
            path.name.lower() in entry_patterns
            or "main" in path.name.lower()
            or "app" in path.name.lower()
        )

    def get_vue_specific_info(self, file_path: str) -> Dict:
        """Extract Vue-specific information like component options, props, etc."""
        try:
            content = self._read_file_content(file_path)
            script_sections = self._extract_script_sections(content)

            vue_info = {
                "has_template": "<template" in content.lower(),
                "has_style": "<style" in content.lower(),
                "script_languages": [lang for _, lang in script_sections],
                "component_name": self._extract_component_name(content),
                "props": self._extract_props(content),
                "computed": self._extract_computed_properties(content),
                "methods": self._extract_methods(content),
            }

            return vue_info

        except Exception as e:
            print(f"Error extracting Vue info from {file_path}: {e}")
            return {}

    def _extract_component_name(self, content: str) -> Optional[str]:
        """Extract the component name from Vue SFC."""
        # Look for name property in component definition
        name_pattern = r'name\s*:\s*[\'"]([^\'"]+)[\'"]'
        match = re.search(name_pattern, content)
        return match.group(1) if match else None

    def _extract_props(self, content: str) -> List[str]:
        """Extract props from Vue component."""
        props = []

        # Simple props array: props: ['prop1', 'prop2']
        simple_props_pattern = r"props\s*:\s*\[([^\]]+)\]"
        match = re.search(simple_props_pattern, content)
        if match:
            prop_names = re.findall(r'[\'"]([^\'"]+)[\'"]', match.group(1))
            props.extend(prop_names)

        # Object props: props: { prop1: Type, prop2: { type: Type } }
        # Use a more sophisticated approach to handle nested objects
        props_start = content.find("props:")
        if props_start != -1:
            # Find the opening brace after 'props:'
            brace_start = content.find("{", props_start)
            if brace_start != -1:
                # Count braces to find the matching closing brace
                brace_count = 1
                pos = brace_start + 1
                while pos < len(content) and brace_count > 0:
                    if content[pos] == "{":
                        brace_count += 1
                    elif content[pos] == "}":
                        brace_count -= 1
                    pos += 1

                if brace_count == 0:
                    props_content = content[brace_start + 1 : pos - 1]
                    # Extract top-level property names (not nested ones)
                    prop_names = re.findall(r"^\s*(\w+)\s*:", props_content, re.MULTILINE)
                    props.extend(prop_names)

        return props

    def _extract_computed_properties(self, content: str) -> List[str]:
        """Extract computed properties from Vue component."""
        computed = []

        # Find computed section using brace counting
        computed_start = content.find("computed:")
        if computed_start != -1:
            # Find the opening brace after 'computed:'
            brace_start = content.find("{", computed_start)
            if brace_start != -1:
                # Count braces to find the matching closing brace
                brace_count = 1
                pos = brace_start + 1
                while pos < len(content) and brace_count > 0:
                    if content[pos] == "{":
                        brace_count += 1
                    elif content[pos] == "}":
                        brace_count -= 1
                    pos += 1

                if brace_count == 0:
                    computed_content = content[brace_start + 1 : pos - 1]
                    # Extract top-level property names
                    computed_names = re.findall(
                        r"^\s*(\w+)\s*[:{(]", computed_content, re.MULTILINE
                    )
                    computed.extend(computed_names)

        return computed

    def _extract_methods(self, content: str) -> List[str]:
        """Extract methods from Vue component."""
        methods = []

        # Find methods section using brace counting
        methods_start = content.find("methods:")
        if methods_start != -1:
            # Find the opening brace after 'methods:'
            brace_start = content.find("{", methods_start)
            if brace_start != -1:
                # Count braces to find the matching closing brace
                brace_count = 1
                pos = brace_start + 1
                while pos < len(content) and brace_count > 0:
                    if content[pos] == "{":
                        brace_count += 1
                    elif content[pos] == "}":
                        brace_count -= 1
                    pos += 1

                if brace_count == 0:
                    methods_content = content[brace_start + 1 : pos - 1]
                    # Extract top-level method names (handle async methods too)
                    method_names = re.findall(
                        r"^\s*(?:async\s+)?(\w+)\s*[:(]", methods_content, re.MULTILINE
                    )
                    methods.extend(method_names)

        return methods
