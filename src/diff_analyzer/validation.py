"""
Validation utilities for the dependency analyzer.

This module provides validation functions for input parameters, file paths,
project structure, and configuration options to ensure robust operation.
"""

import os
import re
from pathlib import Path
from typing import Any, Dict, List, Optional, Set

from .exceptions import ConfigurationError, ProjectStructureError, ValidationError


def validate_project_path(project_path: str) -> Path:
    """
    Validate and normalize a project path.

    Args:
        project_path (str): Path to validate

    Returns:
        Path: Validated and resolved path

    Raises:
        ValidationError: If path is invalid
    """
    if not project_path:
        raise ValidationError("Project path cannot be empty")

    path = Path(project_path)

    if not path.exists():
        raise ValidationError(f"Project path does not exist: {project_path}")

    if not path.is_dir():
        raise ValidationError(f"Project path is not a directory: {project_path}")

    if not os.access(path, os.R_OK):
        raise ValidationError(f"Project path is not readable: {project_path}")

    return path.resolve()


def validate_exclude_patterns(exclude_patterns: List[str]) -> List[str]:
    """
    Validate exclude patterns.

    Args:
        exclude_patterns (List[str]): List of patterns to validate

    Returns:
        List[str]: Validated patterns

    Raises:
        ValidationError: If patterns are invalid
    """
    if not isinstance(exclude_patterns, list):
        raise ValidationError("Exclude patterns must be a list")

    validated_patterns = []
    for pattern in exclude_patterns:
        if not isinstance(pattern, str):
            raise ValidationError(f"Exclude pattern must be a string: {pattern}")

        if not pattern.strip():
            continue  # Skip empty patterns

        # Basic validation - check for dangerous patterns
        if pattern.strip() in ["/", "*", "**"]:
            raise ValidationError(f"Dangerous exclude pattern: {pattern}")

        validated_patterns.append(pattern.strip())

    return validated_patterns


def validate_output_format(format_type: str) -> str:
    """
    Validate output format.

    Args:
        format_type (str): Format to validate

    Returns:
        str: Validated format

    Raises:
        ValidationError: If format is invalid
    """
    valid_formats = {"json", "dot", "mermaid", "text"}

    if not isinstance(format_type, str):
        raise ValidationError("Output format must be a string")

    format_lower = format_type.lower().strip()

    if format_lower not in valid_formats:
        raise ValidationError(
            f"Invalid output format: {format_type}. Valid formats: {', '.join(valid_formats)}"
        )

    return format_lower


def validate_file_path(file_path: str, must_exist: bool = True) -> str:
    """
    Validate a file path.

    Args:
        file_path (str): File path to validate
        must_exist (bool): Whether the file must exist

    Returns:
        str: Validated file path

    Raises:
        ValidationError: If file path is invalid
    """
    if not isinstance(file_path, str):
        raise ValidationError("File path must be a string")

    if not file_path.strip():
        raise ValidationError("File path cannot be empty")

    path = Path(file_path)

    if must_exist:
        if not path.exists():
            raise ValidationError(f"File does not exist: {file_path}")

        if not path.is_file():
            raise ValidationError(f"Path is not a file: {file_path}")

        if not os.access(path, os.R_OK):
            raise ValidationError(f"File is not readable: {file_path}")

    return str(path.resolve())


def validate_supported_extensions(extensions) -> Set[str]:
    """
    Validate file extensions.

    Args:
        extensions: Set or list of extensions to validate

    Returns:
        Set[str]: Validated extensions

    Raises:
        ValidationError: If extensions are invalid
    """
    if not isinstance(extensions, (set, list)):
        raise ValidationError("Extensions must be a set or list")

    validated_extensions = set()

    # Convert to set if it's a list
    if isinstance(extensions, list):
        extensions = set(extensions)

    for ext in extensions:
        if not isinstance(ext, str):
            raise ValidationError(f"Extension must be a string: {ext}")

        if not ext.startswith("."):
            raise ValidationError(f"Extension must start with '.': {ext}")

        if len(ext) < 2:
            raise ValidationError(f"Extension too short: {ext}")

        # Check for valid characters
        if not re.match(r"^\.[a-zA-Z0-9]+$", ext):
            raise ValidationError(f"Invalid extension format: {ext}")

        validated_extensions.add(ext.lower())

    return validated_extensions


def validate_max_workers(max_workers: Optional[int]) -> int:
    """
    Validate max workers parameter.

    Args:
        max_workers (Optional[int]): Number of workers to validate

    Returns:
        int: Validated number of workers

    Raises:
        ValidationError: If max_workers is invalid
    """
    if max_workers is None:
        return min(32, (os.cpu_count() or 1) + 4)

    if not isinstance(max_workers, int):
        raise ValidationError("max_workers must be an integer")

    if max_workers < 1:
        raise ValidationError("max_workers must be at least 1")

    if max_workers > 100:
        raise ValidationError("max_workers cannot exceed 100")

    return max_workers


def validate_project_structure(project_path: Path) -> Dict[str, Any]:
    """
    Validate and analyze project structure.

    Args:
        project_path (Path): Project path to analyze

    Returns:
        Dict[str, Any]: Project structure information

    Raises:
        ProjectStructureError: If project structure is invalid
    """
    structure_info = {
        "has_package_json": False,
        "has_tsconfig": False,
        "has_vue_config": False,
        "has_src_dir": False,
        "estimated_size": "unknown",
        "framework_type": "unknown",
    }

    try:
        # Check for common project files
        if (project_path / "package.json").exists():
            structure_info["has_package_json"] = True

        if (project_path / "tsconfig.json").exists():
            structure_info["has_tsconfig"] = True

        if (project_path / "vue.config.js").exists() or (project_path / "vite.config.js").exists():
            structure_info["has_vue_config"] = True

        if (project_path / "src").is_dir():
            structure_info["has_src_dir"] = True

        # Estimate project size
        file_count = sum(1 for _ in project_path.rglob("*") if _.is_file())
        if file_count < 50:
            structure_info["estimated_size"] = "small"
        elif file_count < 500:
            structure_info["estimated_size"] = "medium"
        else:
            structure_info["estimated_size"] = "large"

        # Detect framework type
        if structure_info["has_vue_config"] or any(project_path.rglob("*.vue")):
            structure_info["framework_type"] = "vue"
        elif any(project_path.rglob("*.jsx")) or any(project_path.rglob("*.tsx")):
            structure_info["framework_type"] = "react"
        elif structure_info["has_tsconfig"]:
            structure_info["framework_type"] = "typescript"
        elif structure_info["has_package_json"]:
            structure_info["framework_type"] = "javascript"

    except Exception as e:
        raise ProjectStructureError(f"Failed to analyze project structure: {e}")

    return structure_info


def validate_configuration(config: Dict[str, Any]) -> Dict[str, Any]:
    """
    Validate configuration dictionary.

    Args:
        config (Dict[str, Any]): Configuration to validate

    Returns:
        Dict[str, Any]: Validated configuration

    Raises:
        ConfigurationError: If configuration is invalid
    """
    if not isinstance(config, dict):
        raise ConfigurationError("Configuration must be a dictionary")

    validated_config = {}

    # Validate known configuration keys
    if "max_workers" in config:
        validated_config["max_workers"] = validate_max_workers(config["max_workers"])

    if "exclude_patterns" in config:
        validated_config["exclude_patterns"] = validate_exclude_patterns(config["exclude_patterns"])

    if "supported_extensions" in config:
        validated_config["supported_extensions"] = validate_supported_extensions(
            config["supported_extensions"]
        )

    if "output_format" in config:
        validated_config["output_format"] = validate_output_format(config["output_format"])

    # Preserve other known configuration keys without specific validation
    for key in [
        "parallel_processing", "cache_enabled", "cache_dir", "max_file_size",
        "verbose", "show_external_deps", "show_details", "find_cycles",
        "mermaid_type", "custom_parsers", "analysis_rules", "output_options",
        "vue_settings", "react_settings", "integrations", "reporting"
    ]:
        if key in config:
            validated_config[key] = config[key]

    # Check for unknown configuration keys (allow additional keys for flexibility)
    known_keys = {
        "max_workers",
        "exclude_patterns",
        "supported_extensions",
        "output_format",
        "parallel_processing",
        "cache_enabled",
        "cache_dir",
        "max_file_size",
        "verbose",
        "show_external_deps",
        "show_details",
        "find_cycles",
        "mermaid_type",
        "custom_parsers",
        "analysis_rules",
        "output_options",
        "vue_settings",
        "react_settings",
        "integrations",
        "reporting",
    }
    unknown_keys = set(config.keys()) - known_keys

    # Only warn about truly unknown keys, don't fail
    if unknown_keys:
        print(f"Warning: Unknown configuration keys (will be ignored): {', '.join(unknown_keys)}")
        # Remove unknown keys from validated config
        for key in unknown_keys:
            config.pop(key, None)

    return validated_config
