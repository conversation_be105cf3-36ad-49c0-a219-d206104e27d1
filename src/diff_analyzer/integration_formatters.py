"""
Integration formatters for development workflows.

This module provides output formatters designed for integration with
popular development tools, CI/CD pipelines, and documentation systems.
"""

import csv
import io
import json
import xml.etree.ElementTree as ET
from pathlib import Path
from typing import Any, Dict, List

from .output_formatters import OutputFormatter


class CSVFormatter(OutputFormatter):
    """
    CSV formatter for dependency data.

    Exports dependency information in CSV format for analysis
    in spreadsheet applications or data processing tools.
    """

    def format(self, format_type: str = "dependencies") -> str:
        """
        Format dependency data as CSV.

        Args:
            format_type (str): Type of CSV to generate ('dependencies', 'files', 'metrics')

        Returns:
            str: CSV formatted data
        """
        if format_type == "dependencies":
            return self._format_dependencies_csv()
        elif format_type == "files":
            return self._format_files_csv()
        elif format_type == "metrics":
            return self._format_metrics_csv()
        else:
            raise ValueError(f"Unsupported CSV format type: {format_type}")

    def _format_dependencies_csv(self) -> str:
        """Format dependencies as CSV."""
        output = io.StringIO()
        writer = csv.writer(output)

        # Header
        writer.writerow(
            [
                "Source File",
                "Target File",
                "Import Type",
                "Dependency Type",
                "Imported Names",
                "Line Number",
            ]
        )

        # Data rows
        for source, target, data in self.graph.graph.edges(data=True):
            source_rel = self._get_relative_path(source)
            target_rel = self._get_relative_path(target)

            writer.writerow(
                [
                    source_rel,
                    target_rel,
                    data.get("import_type", ""),
                    "local",  # All graph edges are local dependencies
                    ", ".join(data.get("imported_names", [])),
                    data.get("line_number", ""),
                ]
            )

        return output.getvalue()

    def _format_files_csv(self) -> str:
        """Format file information as CSV."""
        output = io.StringIO()
        writer = csv.writer(output)

        # Header
        writer.writerow(
            [
                "File Path",
                "File Type",
                "Import Count",
                "Export Count",
                "Local Dependencies",
                "External Dependencies",
                "Is Entry Point",
                "Is Leaf Node",
                "Fan In",
                "Fan Out",
            ]
        )

        # Data rows
        for file_path, node in self.graph.nodes.items():
            rel_path = self._get_relative_path(file_path)

            writer.writerow(
                [
                    rel_path,
                    Path(file_path).suffix,
                    len(node.imports),
                    len(node.exports),
                    len([imp for imp in node.imports if imp.dependency_type.name == "LOCAL"]),
                    len([imp for imp in node.imports if imp.dependency_type.name == "EXTERNAL"]),
                    node.is_entry_point,
                    node.is_leaf_node,
                    self.graph.graph.in_degree(file_path),
                    self.graph.graph.out_degree(file_path),
                ]
            )

        return output.getvalue()

    def _format_metrics_csv(self) -> str:
        """Format metrics as CSV."""
        try:
            metrics = self.graph.get_advanced_metrics()
        except Exception:
            # Fallback to basic metrics if advanced metrics fail
            metrics = {"basic_metrics": self.graph.get_statistics()}

        output = io.StringIO()
        writer = csv.writer(output)

        # Header
        writer.writerow(["Metric Category", "Metric Name", "Value"])

        # Flatten metrics for CSV
        for category, category_metrics in metrics.items():
            if isinstance(category_metrics, dict):
                for metric_name, value in category_metrics.items():
                    if isinstance(value, (int, float, str, bool)):
                        writer.writerow([category, metric_name, value])

        return output.getvalue()


class XMLFormatter(OutputFormatter):
    """
    XML formatter for dependency data.

    Exports dependency information in XML format for integration
    with XML-based tools and systems.
    """

    def format(self, include_metrics: bool = False) -> str:
        """
        Format dependency data as XML.

        Args:
            include_metrics (bool): Whether to include metrics in output

        Returns:
            str: XML formatted data
        """
        root = ET.Element("dependency_analysis")
        root.set("project_root", str(self.graph.project_root))

        # Project info
        project_info = ET.SubElement(root, "project_info")
        for key, value in self.graph.project_info.items():
            elem = ET.SubElement(project_info, key)
            elem.text = str(value)

        # Statistics
        stats = self.graph.get_statistics()
        statistics = ET.SubElement(root, "statistics")
        for key, value in stats.items():
            elem = ET.SubElement(statistics, key)
            elem.text = str(value)

        # Files
        files = ET.SubElement(root, "files")
        for file_path, node in self.graph.nodes.items():
            file_elem = ET.SubElement(files, "file")
            file_elem.set("path", self._get_relative_path(file_path))
            file_elem.set("type", Path(file_path).suffix)
            file_elem.set("is_entry_point", str(node.is_entry_point))
            file_elem.set("is_leaf_node", str(node.is_leaf_node))

            # Imports
            imports = ET.SubElement(file_elem, "imports")
            for imp in node.imports:
                import_elem = ET.SubElement(imports, "import")
                import_elem.set("module", imp.imported_module)
                import_elem.set("type", imp.import_type.name)
                import_elem.set("dependency_type", imp.dependency_type.name)
                if imp.line_number:
                    import_elem.set("line", str(imp.line_number))

                # Imported names
                for name in imp.imported_names:
                    name_elem = ET.SubElement(import_elem, "imported_name")
                    name_elem.text = name

            # Exports
            exports = ET.SubElement(file_elem, "exports")
            for export in node.exports:
                export_elem = ET.SubElement(exports, "export")
                export_elem.text = export

        # Dependencies
        dependencies = ET.SubElement(root, "dependencies")
        for source, target, data in self.graph.graph.edges(data=True):
            dep_elem = ET.SubElement(dependencies, "dependency")
            dep_elem.set("source", self._get_relative_path(source))
            dep_elem.set("target", self._get_relative_path(target))
            dep_elem.set("import_type", data.get("import_type", ""))
            if data.get("line_number"):
                dep_elem.set("line", str(data["line_number"]))

        # Metrics (if requested)
        if include_metrics:
            try:
                metrics_data = self.graph.get_advanced_metrics()
                metrics_elem = ET.SubElement(root, "metrics")
                self._add_metrics_to_xml(metrics_elem, metrics_data)
            except Exception:
                pass  # Skip metrics if they fail

        return ET.tostring(root, encoding="unicode", xml_declaration=True)

    def _add_metrics_to_xml(self, parent: ET.Element, metrics: Dict[str, Any]) -> None:
        """Add metrics to XML element."""
        for key, value in metrics.items():
            if isinstance(value, dict):
                sub_elem = ET.SubElement(parent, key)
                self._add_metrics_to_xml(sub_elem, value)
            elif isinstance(value, list):
                sub_elem = ET.SubElement(parent, key)
                for i, item in enumerate(value):
                    item_elem = ET.SubElement(sub_elem, "item")
                    item_elem.set("index", str(i))
                    if isinstance(item, dict):
                        self._add_metrics_to_xml(item_elem, item)
                    else:
                        item_elem.text = str(item)
            else:
                elem = ET.SubElement(parent, key)
                elem.text = str(value)


class GitHubActionsFormatter(OutputFormatter):
    """
    GitHub Actions formatter for CI/CD integration.

    Generates output suitable for GitHub Actions workflows,
    including annotations and job summaries.
    """

    def format(self, format_type: str = "summary") -> str:
        """
        Format for GitHub Actions.

        Args:
            format_type (str): Type of output ('summary', 'annotations', 'pr-comment')

        Returns:
            str: GitHub Actions formatted output
        """
        if format_type == "summary":
            return self._format_job_summary()
        elif format_type == "annotations":
            return self._format_annotations()
        elif format_type == "pr-comment":
            return self._format_pr_comment()
        else:
            raise ValueError(f"Unsupported GitHub Actions format: {format_type}")

    def _format_job_summary(self) -> str:
        """Format as GitHub Actions job summary."""
        stats = self.graph.get_statistics()

        summary = f"""# Dependency Analysis Summary

## 📊 Project Statistics
- **Total Files:** {stats['total_files']}
- **Total Dependencies:** {stats['total_dependencies']}
- **Entry Points:** {stats['entry_points']}
- **Leaf Nodes:** {stats['leaf_nodes']}
- **Circular Dependencies:** {stats.get('circular_dependencies', 0)}

## 🎯 Key Metrics
- **Average Dependencies per File:** {stats['total_dependencies'] / max(stats['total_files'], 1):.1f}
- **Dependency Health:** {
    '✅ Good' if stats.get('circular_dependencies', 0) == 0 else '⚠️ Issues Found'
}

"""

        # Add warnings if any
        if stats.get("circular_dependencies", 0) > 0:
            summary += f"""## ⚠️ Warnings
- Found {stats['circular_dependencies']} circular dependencies that should be resolved

"""

        # Add file breakdown
        framework_dist = self._get_framework_distribution()
        if framework_dist:
            summary += "## 📁 File Distribution\n"
            for framework, count in framework_dist.items():
                if count > 0:
                    summary += f"- **{framework.title()}:** {count} files\n"

        return summary

    def _format_annotations(self) -> str:
        """Format as GitHub Actions annotations."""
        annotations = []

        # Check for circular dependencies
        try:
            cycles = self.graph.find_circular_dependencies()
            for cycle in cycles:
                # Create annotation for first file in cycle
                if cycle:
                    file_path = self._get_relative_path(cycle[0])
                    cycle_str = " -> ".join([self._get_relative_path(f) for f in cycle])
                    annotations.append(
                        f"::warning file={file_path}::Circular dependency detected: {cycle_str}"
                    )
        except Exception:
            pass

        # Check for high coupling
        try:
            metrics = self.graph.get_advanced_metrics()
            maintainability = metrics.get("maintainability_metrics", {})
            high_coupling = maintainability.get("high_coupling_files", [])

            for file_path, coupling in high_coupling[:5]:  # Limit to top 5
                rel_path = self._get_relative_path(file_path)
                annotations.append(
                    f"::notice file={rel_path}::High coupling detected (score: {coupling})"
                )
        except Exception:
            pass

        return "\n".join(annotations)

    def _format_pr_comment(self) -> str:
        """Format as PR comment."""
        stats = self.graph.get_statistics()

        comment = f"""## 🔍 Dependency Analysis Report

### Summary
- **Files Analyzed:** {stats['total_files']}
- **Dependencies Found:** {stats['total_dependencies']}
- **Circular Dependencies:** {stats.get('circular_dependencies', 0)}

"""

        if stats.get("circular_dependencies", 0) > 0:
            comment += f"""### ⚠️ Issues Found
- **{stats['circular_dependencies']} circular dependencies** detected that may impact
  maintainability

"""
        else:
            comment += "### ✅ No Issues Found\nNo circular dependencies detected.\n\n"

        # Add recommendations
        try:
            from .advanced_formatters import MetricsJSONFormatter

            formatter = MetricsJSONFormatter(self.graph)
            recommendations = formatter._generate_recommendations()

            if recommendations["high_priority"]:
                comment += "### 🚨 High Priority Recommendations\n"
                for rec in recommendations["high_priority"][:3]:  # Limit to top 3
                    comment += (
                        f"- **{rec['type'].replace('_', ' ').title()}:** {rec['description']}\n"
                    )
                comment += "\n"
        except Exception:
            pass

        comment += "_Generated by Vue.js and React Dependency Analyzer_"

        return comment

    def _get_framework_distribution(self) -> Dict[str, int]:
        """Get distribution of files by framework."""
        distribution = {"vue": 0, "react": 0, "typescript": 0, "javascript": 0}

        for file_path in self.graph.nodes.keys():
            ext = Path(file_path).suffix
            if ext == ".vue":
                distribution["vue"] += 1
            elif ext in [".jsx", ".tsx"]:
                distribution["react"] += 1
            elif ext == ".ts":
                distribution["typescript"] += 1
            elif ext == ".js":
                distribution["javascript"] += 1

        return distribution


class SARIFFormatter(OutputFormatter):
    """
    SARIF formatter for security and code quality tools.

    Exports dependency analysis results in SARIF format for integration
    with security scanners and code quality tools.
    """

    def format(self) -> str:
        """
        Format dependency analysis as SARIF.

        Returns:
            str: SARIF formatted JSON
        """
        sarif = {
            "$schema": (
                "https://raw.githubusercontent.com/oasis-tcs/sarif-spec/master/"
                "Schemata/sarif-schema-2.1.0.json"
            ),
            "version": "2.1.0",
            "runs": [
                {
                    "tool": {
                        "driver": {
                            "name": "Vue.js and React Dependency Analyzer",
                            "version": "0.1.0",
                            "informationUri": "https://github.com/your-org/diff-analyzer",
                            "rules": self._get_sarif_rules(),
                        }
                    },
                    "results": self._get_sarif_results(),
                }
            ],
        }

        return json.dumps(sarif, indent=2)

    def _get_sarif_rules(self) -> List[Dict[str, Any]]:
        """Get SARIF rules definitions."""
        return [
            {
                "id": "circular-dependency",
                "name": "CircularDependency",
                "shortDescription": {"text": "Circular dependency detected"},
                "fullDescription": {
                    "text": (
                        "A circular dependency was detected between files, which can lead to "
                        "maintenance issues and potential runtime problems."
                    )
                },
                "defaultConfiguration": {"level": "warning"},
                "helpUri": "https://docs.example.com/circular-dependencies",
            },
            {
                "id": "high-coupling",
                "name": "HighCoupling",
                "shortDescription": {"text": "High coupling detected"},
                "fullDescription": {
                    "text": (
                        "This file has high coupling (many dependencies), which may indicate "
                        "it should be refactored."
                    )
                },
                "defaultConfiguration": {"level": "note"},
                "helpUri": "https://docs.example.com/coupling",
            },
        ]

    def _get_sarif_results(self) -> List[Dict[str, Any]]:
        """Get SARIF results."""
        results = []

        # Add circular dependency results
        try:
            cycles = self.graph.find_circular_dependencies()
            for cycle in cycles:
                if cycle:
                    file_path = self._get_relative_path(cycle[0])
                    cycle_str = " -> ".join([self._get_relative_path(f) for f in cycle])

                    results.append(
                        {
                            "ruleId": "circular-dependency",
                            "message": {"text": f"Circular dependency: {cycle_str}"},
                            "locations": [
                                {
                                    "physicalLocation": {
                                        "artifactLocation": {"uri": file_path},
                                        "region": {"startLine": 1},
                                    }
                                }
                            ],
                        }
                    )
        except Exception:
            pass

        # Add high coupling results
        try:
            for file_path in self.graph.nodes.keys():
                coupling = self.graph.graph.degree(file_path)
                if coupling > 10:  # High coupling threshold
                    rel_path = self._get_relative_path(file_path)
                    results.append(
                        {
                            "ruleId": "high-coupling",
                            "message": {"text": f"High coupling detected (degree: {coupling})"},
                            "locations": [
                                {
                                    "physicalLocation": {
                                        "artifactLocation": {"uri": rel_path},
                                        "region": {"startLine": 1},
                                    }
                                }
                            ],
                        }
                    )
        except Exception:
            pass

        return results
