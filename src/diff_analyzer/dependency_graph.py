"""
Dependency Graph Analyzer

This module provides comprehensive dependency graph analysis for Vue.js and React projects.
It builds directed graphs of file dependencies, detects circular dependencies, analyzes
project structure, and provides various metrics and insights about the codebase.

Key features:
- Multi-framework support (Vue.js, React, TypeScript)
- Circular dependency detection
- Entry point and leaf node identification
- External dependency tracking
- Project statistics and metrics
- Path analysis between files
"""

import os
import threading
from collections import defaultdict
from concurrent.futures import ThreadPoolExecutor, as_completed
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple

import networkx as nx

from .config import load_config
from .dependency_analyzer import DependencyNode, DependencyType
from .exceptions import (
    DependencyAnalyzerError,
    GraphAnalysisError,
    ParseError,
    ProjectStructureError,
)
from .metrics import DependencyMetrics
from .react_parser import ReactParser
from .validation import (
    validate_exclude_patterns,
    validate_max_workers,
    validate_project_path,
    validate_project_structure,
)
from .vue_parser import VueParser


class DependencyGraph:
    """
    Manages and analyzes dependency relationships in a project.

    This class builds a directed graph representation of file dependencies,
    providing methods for analysis, circular dependency detection, and
    various project metrics.

    Attributes:
        project_root (Path): Absolute path to the project root directory
        nodes (Dict[str, DependencyNode]): Mapping of file paths to dependency nodes
        graph (nx.DiGraph): NetworkX directed graph of dependencies
        vue_parser (VueParser): Parser for Vue.js files
        react_parser (ReactParser): Parser for React/TypeScript files
        supported_extensions (Set[str]): File extensions that can be analyzed

    Examples:
        >>> graph = DependencyGraph("/path/to/project")
        >>> graph.analyze_project()
        >>> stats = graph.get_statistics()
        >>> print(f"Total files: {stats['total_files']}")
        >>>
        >>> cycles = graph.find_circular_dependencies()
        >>> if cycles:
        ...     print(f"Found {len(cycles)} circular dependencies")
    """

    def __init__(
        self,
        project_root: str,
        max_workers: Optional[int] = None,
        config_file: Optional[str] = None,
    ):
        """
        Initialize the dependency graph analyzer with validation and configuration.

        Args:
            project_root (str): Path to the project root directory
            max_workers (Optional[int]): Maximum number of worker threads for parallel processing
            config_file (Optional[str]): Path to configuration file

        Raises:
            ValidationError: If project_root is invalid
            ProjectStructureError: If project structure is problematic
            ConfigurationError: If configuration loading fails

        Examples:
            >>> graph = DependencyGraph("/path/to/vue-project")
            >>> graph = DependencyGraph("/path/to/react-project", max_workers=4)
            >>> graph = DependencyGraph("/path/to/project", config_file="custom.json")
        """
        # Validate inputs
        self.project_root = validate_project_path(project_root)

        # Load configuration
        self.config = load_config(str(self.project_root), config_file)

        # Override with explicit parameters
        if max_workers is not None:
            self.config.set("max_workers", validate_max_workers(max_workers))

        self.max_workers = validate_max_workers(self.config.get("max_workers"))

        # Analyze project structure
        try:
            self.project_info = validate_project_structure(self.project_root)
        except Exception as e:
            raise ProjectStructureError(f"Failed to analyze project structure: {e}")

        # Initialize data structures
        self.nodes: Dict[str, DependencyNode] = {}
        self.graph = nx.DiGraph()
        self.errors: List[DependencyAnalyzerError] = []

        # Initialize parsers
        self.vue_parser = VueParser()
        self.react_parser = ReactParser()

        # Initialize parsers with thread-local storage for thread safety
        self._local = threading.local()

        # File extensions to process (from config)
        extensions = self.config.get("supported_extensions", [".vue", ".jsx", ".tsx", ".ts", ".js"])
        self.supported_extensions = set(extensions)

        # Cache for path resolution
        self._path_cache: Dict[str, Optional[str]] = {}

    def analyze_project(
        self, exclude_patterns: Optional[List[str]] = None, parallel: bool = True
    ) -> None:
        """
        Analyze the entire project and build the dependency graph with error handling.

        Scans the project directory for supported files, parses each file
        to extract dependencies, and builds a directed graph of relationships.

        Args:
            exclude_patterns (Optional[List[str]]): Patterns to exclude from analysis.
                Defaults to common build/dependency directories.
            parallel (bool): Whether to use parallel processing for file parsing.

        Raises:
            ValidationError: If exclude_patterns are invalid
            GraphAnalysisError: If graph analysis fails

        Examples:
            >>> graph = DependencyGraph("/path/to/project")
            >>> graph.analyze_project()  # Use default exclusions with parallel processing
            >>>
            >>> # Custom exclusions with sequential processing
            >>> graph.analyze_project(exclude_patterns=['node_modules', 'dist'], parallel=False)
        """
        try:
            # Validate and set defaults from config
            if exclude_patterns is None:
                exclude_patterns = self.config.get(
                    "exclude_patterns", ["node_modules", ".git", "dist", "build", ".next", ".nuxt"]
                )
            else:
                exclude_patterns = validate_exclude_patterns(exclude_patterns)

            # Use config for parallel processing if not explicitly set
            if parallel is None:
                parallel = self.config.get("parallel_processing", True)

            # Clear previous state
            self.nodes.clear()
            self.graph.clear()
            self.errors.clear()
            self._path_cache.clear()

            # Find all relevant files
            files_to_analyze = self._find_files_to_analyze(exclude_patterns)

            if not files_to_analyze:
                raise GraphAnalysisError("No supported files found in project")

            if self.config.get("verbose", False):
                print(f"Found {len(files_to_analyze)} files to analyze")
                print(f"Using configuration from: {', '.join(self.config.get_config_sources())}")

            # Parse files (parallel or sequential)
            use_parallel = (
                parallel
                and len(files_to_analyze) > 10
                and self.config.get("parallel_processing", True)
            )
            if use_parallel:
                self._parse_files_parallel(files_to_analyze)
            else:
                self._parse_files_sequential(files_to_analyze)

            if not self.nodes:
                raise GraphAnalysisError("No files were successfully parsed")

            # Build edges based on imports
            self._build_graph_edges()

            # Analyze graph properties
            self._analyze_graph_properties()

            # Report any errors encountered
            if self.errors:
                print(f"Analysis completed with {len(self.errors)} errors:")
                for error in self.errors[:5]:  # Show first 5 errors
                    print(f"  - {error}")
                if len(self.errors) > 5:
                    print(f"  ... and {len(self.errors) - 5} more errors")

        except Exception as e:
            if isinstance(e, DependencyAnalyzerError):
                raise
            else:
                raise GraphAnalysisError(f"Unexpected error during analysis: {e}") from e

    def _find_files_to_analyze(self, exclude_patterns: List[str]) -> List[str]:
        """Find all files that should be analyzed with optimized directory traversal."""
        files = []
        exclude_set = set(exclude_patterns)  # Convert to set for O(1) lookup

        for root, dirs, filenames in os.walk(self.project_root):
            # Skip excluded directories more efficiently
            dirs[:] = [d for d in dirs if not any(pattern in d for pattern in exclude_set)]

            # Pre-filter filenames by extension
            relevant_files = [f for f in filenames if Path(f).suffix in self.supported_extensions]

            for filename in relevant_files:
                file_path = Path(root) / filename
                file_path_str = str(file_path)

                # Skip if file path contains excluded patterns
                if not any(pattern in file_path_str for pattern in exclude_set):
                    files.append(file_path_str)

        return files

    def _parse_files_sequential(self, files_to_analyze: List[str]) -> None:
        """Parse files sequentially with error handling."""
        for file_path in files_to_analyze:
            try:
                node = self._parse_file(file_path)
                if node:
                    self.nodes[file_path] = node
                    self.graph.add_node(file_path, **self._node_to_attributes(node))
            except Exception as e:
                error = ParseError(f"Failed to parse file: {e}", file_path)
                self.errors.append(error)
                print(f"Warning: {error}")

    def _parse_files_parallel(self, files_to_analyze: List[str]) -> None:
        """Parse files in parallel using ThreadPoolExecutor with error handling."""
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # Submit all parsing tasks
            future_to_file = {
                executor.submit(self._parse_file_thread_safe, file_path): file_path
                for file_path in files_to_analyze
            }

            # Collect results as they complete
            for future in as_completed(future_to_file):
                file_path = future_to_file[future]
                try:
                    node = future.result()
                    if node:
                        self.nodes[file_path] = node
                        self.graph.add_node(file_path, **self._node_to_attributes(node))
                except Exception as e:
                    error = ParseError(f"Failed to parse file: {e}", file_path)
                    self.errors.append(error)
                    print(f"Warning: {error}")

    def _parse_file_thread_safe(self, file_path: str) -> Optional[DependencyNode]:
        """Thread-safe version of _parse_file that uses thread-local parsers."""
        try:
            # Get thread-local parsers
            if not hasattr(self._local, "vue_parser"):
                self._local.vue_parser = VueParser()
                self._local.react_parser = ReactParser()

            ext = Path(file_path).suffix.lower()

            if ext == ".vue":
                return self._local.vue_parser.parse_file(file_path)
            elif ext in [".jsx", ".tsx", ".ts", ".js"]:
                return self._local.react_parser.parse_file(file_path)
            else:
                return None

        except Exception as e:
            print(f"Error parsing {file_path}: {e}")
            return None

    def _parse_file(self, file_path: str) -> Optional[DependencyNode]:
        """Parse a single file using the appropriate parser."""
        try:
            ext = Path(file_path).suffix.lower()

            if ext == ".vue":
                return self.vue_parser.parse_file(file_path)
            elif ext in [".jsx", ".tsx", ".ts", ".js"]:
                return self.react_parser.parse_file(file_path)
            else:
                print(f"Unsupported file type: {file_path}")
                return None

        except Exception as e:
            print(f"Error parsing {file_path}: {e}")
            return None

    def _node_to_attributes(self, node: DependencyNode) -> Dict[str, Any]:
        """
        Convert DependencyNode to graph node attributes for NetworkX.

        Creates a dictionary of attributes that can be attached to NetworkX graph nodes
        for analysis and visualization purposes.

        Args:
            node (DependencyNode): The dependency node to convert

        Returns:
            Dict[str, Any]: Dictionary containing node attributes including:
                - imports_count: Number of import statements
                - exports_count: Number of exports
                - exports: List of exported names
                - is_entry_point: Whether this is an entry point
                - is_leaf_node: Whether this is a leaf node
                - file_type: File extension
                - local_imports: Count of local imports
                - external_imports: Count of external imports
        """
        return {
            "imports_count": len(node.imports),
            "exports_count": len(node.exports),
            "exports": node.exports,
            "import_count": len(node.imports),  # Add this for test compatibility
            "is_entry_point": node.is_entry_point,
            "is_leaf_node": node.is_leaf_node,
            "file_type": Path(node.file_path).suffix,
            "local_imports": len(
                [imp for imp in node.imports if imp.dependency_type == DependencyType.LOCAL]
            ),
            "external_imports": len(
                [imp for imp in node.imports if imp.dependency_type == DependencyType.EXTERNAL]
            ),
        }

    def _build_graph_edges(self) -> None:
        """Build edges in the graph based on import relationships."""
        for source_file, node in self.nodes.items():
            for import_info in node.imports:
                # Only create edges for local dependencies
                if import_info.dependency_type == DependencyType.LOCAL:
                    target_file = self._resolve_local_import(
                        import_info.imported_module, source_file
                    )

                    if target_file and target_file in self.nodes:
                        self.graph.add_edge(
                            source_file,
                            target_file,
                            **{
                                "import_type": import_info.import_type.value,
                                "imported_names": import_info.imported_names,
                                "line_number": import_info.line_number,
                            },
                        )

    def _resolve_local_import(self, import_path: str, source_file: str) -> Optional[str]:
        """Resolve a local import path to an actual file path with caching."""
        # Create cache key
        cache_key = f"{import_path}:{source_file}"
        if cache_key in self._path_cache:
            return self._path_cache[cache_key]

        source_dir = Path(source_file).parent
        potential_paths = []

        # If import_path is already an absolute path and exists in nodes, return it directly
        if import_path in self.nodes:
            self._path_cache[cache_key] = import_path
            return import_path

        if import_path.startswith("."):
            # Relative import
            resolved_path = (source_dir / import_path).resolve()
            potential_paths.append(resolved_path)

            # Try with different extensions
            for ext in self.supported_extensions:
                potential_paths.append(resolved_path.with_suffix(ext))

            # Try index files
            if resolved_path.is_dir():
                for ext in self.supported_extensions:
                    potential_paths.append(resolved_path / f"index{ext}")
        elif import_path.startswith("/"):
            # Absolute path - check if it exists directly
            potential_paths.append(Path(import_path))

            # Try with different extensions
            for ext in self.supported_extensions:
                potential_paths.append(Path(import_path).with_suffix(ext))
        else:
            # Project-relative import (like 'src/types/User')
            # Try resolving from project root
            project_root_path = self.project_root / import_path
            potential_paths.append(project_root_path)

            # Try with different extensions
            for ext in self.supported_extensions:
                potential_paths.append(project_root_path.with_suffix(ext))

            # Try index files
            if project_root_path.is_dir():
                for ext in self.supported_extensions:
                    potential_paths.append(project_root_path / f"index{ext}")

        # Find the first existing file
        result = None
        for path in potential_paths:
            path_str = str(path)
            if path_str in self.nodes:
                result = path_str
                break

        # Cache the result (even if None)
        self._path_cache[cache_key] = result
        return result

    def get_errors(self) -> List[DependencyAnalyzerError]:
        """
        Get list of errors encountered during analysis.

        Returns:
            List[DependencyAnalyzerError]: List of errors
        """
        return self.errors.copy()

    def has_errors(self) -> bool:
        """
        Check if any errors were encountered during analysis.

        Returns:
            bool: True if errors were encountered
        """
        return len(self.errors) > 0

    def get_error_summary(self) -> Dict[str, int]:
        """
        Get summary of errors by type.

        Returns:
            Dict[str, int]: Error counts by type
        """
        error_counts = {}
        for error in self.errors:
            error_type = type(error).__name__
            error_counts[error_type] = error_counts.get(error_type, 0) + 1
        return error_counts

    def get_advanced_metrics(self) -> Dict[str, Any]:
        """
        Get comprehensive advanced metrics for the project.

        Returns:
            Dict[str, Any]: Complete metrics analysis including complexity,
                           architectural patterns, maintainability, and quality indicators
        """
        metrics_calculator = DependencyMetrics(self)
        return metrics_calculator.calculate_all_metrics()

    def get_complexity_analysis(self) -> Dict[str, Any]:
        """
        Get complexity analysis for the project.

        Returns:
            Dict[str, Any]: Complexity metrics and analysis
        """
        metrics_calculator = DependencyMetrics(self)
        return {
            "basic_metrics": metrics_calculator.get_basic_metrics(),
            "complexity_metrics": metrics_calculator.get_complexity_metrics(),
            "architectural_metrics": metrics_calculator.get_architectural_metrics(),
        }

    def get_maintainability_analysis(self) -> Dict[str, Any]:
        """
        Get maintainability analysis for the project.

        Returns:
            Dict[str, Any]: Maintainability and quality metrics
        """
        metrics_calculator = DependencyMetrics(self)
        return {
            "maintainability_metrics": metrics_calculator.get_maintainability_metrics(),
            "quality_metrics": metrics_calculator.get_quality_metrics(),
        }

    def get_performance_insights(self) -> Dict[str, Any]:
        """
        Get performance-related insights for the project.

        Returns:
            Dict[str, Any]: Performance metrics and optimization suggestions
        """
        metrics_calculator = DependencyMetrics(self)
        return metrics_calculator.get_performance_metrics()

    def get_file_analysis(self, file_path: str) -> Optional[Dict[str, Any]]:
        """
        Get detailed analysis for a specific file.

        Args:
            file_path (str): Path to the file to analyze

        Returns:
            Optional[Dict[str, Any]]: File analysis or None if file not found
        """
        if file_path not in self.nodes:
            return None

        metrics_calculator = DependencyMetrics(self)
        file_metrics = metrics_calculator.get_file_level_metrics()

        return {
            "file_metrics": file_metrics.get(file_path, {}),
            "dependencies": self.get_dependencies(file_path),
            "dependents": self.get_dependents(file_path),
            "dependency_chain": self.get_dependency_chain(file_path),
        }

    def _analyze_graph_properties(self) -> None:
        """Analyze various properties of the dependency graph."""
        # Analyze each node to determine its role in the dependency graph
        for file_path, node in self.nodes.items():
            # Entry point detection: files that import others but aren't imported
            # These are typically main.ts, index.tsx, or app entry files
            in_degree = self.graph.in_degree(file_path)
            out_degree = self.graph.out_degree(file_path)

            # A file is an entry point if:
            # 1. It has no incoming dependencies (nothing imports it)
            # 2. It has outgoing dependencies (it imports other files)
            # 3. OR it was already marked as entry point during parsing (main.ts, etc.)
            if (in_degree == 0 and out_degree > 0) or node.is_entry_point:
                node.is_entry_point = True
                self.graph.nodes[file_path]["is_entry_point"] = True

            # Leaf node detection: files that don't import anything but are imported
            # These are typically utility files, components with no dependencies
            # A file is a leaf node if:
            # 1. It has no outgoing dependencies (imports nothing)
            # 2. It has incoming dependencies (other files import it)
            # 3. OR it was already marked as leaf during parsing
            if (out_degree == 0 and in_degree > 0) or node.is_leaf_node:
                node.is_leaf_node = True
                self.graph.nodes[file_path]["is_leaf_node"] = True

    def find_circular_dependencies(self) -> List[List[str]]:
        """Find all circular dependencies in the graph."""
        try:
            # Use NetworkX's simple_cycles algorithm to find all cycles
            # This algorithm finds all elementary cycles (cycles with no repeated nodes)
            # in the directed graph, which correspond to circular dependencies
            cycles = list(nx.simple_cycles(self.graph))

            # Include self-loops as they are also circular dependencies
            # Check for self-loops explicitly since simple_cycles might not catch them
            for node in self.graph.nodes():
                if self.graph.has_edge(node, node):
                    cycles.append([node])

            return cycles
        except nx.NetworkXError:
            # Handle cases where the graph might be malformed or empty
            return []

    def get_entry_points(self) -> List[str]:
        """Get all entry points in the project."""
        return [
            file_path
            for file_path, node in self.nodes.items()
            if node.is_entry_point or self.graph.in_degree(file_path) == 0
        ]

    def get_leaf_nodes(self) -> List[str]:
        """Get all leaf nodes (files with no dependencies)."""
        return [
            file_path
            for file_path, node in self.nodes.items()
            if node.is_leaf_node or self.graph.out_degree(file_path) == 0
        ]

    def get_dependency_chain(self, file_path: str) -> List[str]:
        """Get the dependency chain for a specific file."""
        if file_path not in self.graph:
            return []

        # Use DFS to get all dependencies
        visited = set()
        chain = []

        def dfs(current_file):
            if current_file in visited:
                return

            visited.add(current_file)
            chain.append(current_file)

            for successor in self.graph.successors(current_file):
                dfs(successor)

        dfs(file_path)
        return chain

    def get_dependents(self, file_path: str) -> List[str]:
        """Get all files that depend on the given file."""
        if file_path not in self.graph:
            return []

        return list(self.graph.predecessors(file_path))

    def get_dependencies(self, file_path: str) -> List[str]:
        """Get all direct dependencies of the given file."""
        if file_path not in self.graph:
            return []

        return list(self.graph.successors(file_path))

    def get_external_dependencies(self) -> Dict[str, List[str]]:
        """Get all external dependencies grouped by package."""
        external_deps = defaultdict(list)

        for file_path, node in self.nodes.items():
            for import_info in node.imports:
                if import_info.dependency_type == DependencyType.EXTERNAL:
                    package_name = import_info.imported_module.split("/")[0]
                    external_deps[package_name].append(file_path)

        return dict(external_deps)

    def get_statistics(self) -> Dict[str, Any]:
        """Get comprehensive statistics about the dependency graph."""
        total_files = len(self.nodes)
        total_edges = self.graph.number_of_edges()

        # Calculate various metrics
        in_degrees = [self.graph.in_degree(node) for node in self.graph.nodes()]
        out_degrees = [self.graph.out_degree(node) for node in self.graph.nodes()]

        external_deps = self.get_external_dependencies()
        cycles = self.find_circular_dependencies()

        stats = {
            "total_files": total_files,
            "total_dependencies": total_edges,
            "entry_points": len(self.get_entry_points()),
            "leaf_nodes": len(self.get_leaf_nodes()),
            "circular_dependencies": len(cycles),
            "external_packages": len(external_deps),
            "avg_dependencies_per_file": sum(out_degrees) / total_files if total_files > 0 else 0,
            "avg_dependents_per_file": sum(in_degrees) / total_files if total_files > 0 else 0,
            "max_dependencies": max(out_degrees) if out_degrees else 0,
            "max_dependents": max(in_degrees) if in_degrees else 0,
            "file_types": self._get_file_type_distribution(),
            "most_imported_files": self._get_most_imported_files(5),
            "most_dependent_files": self._get_most_dependent_files(5),
        }

        return stats

    def _get_file_type_distribution(self) -> Dict[str, int]:
        """Get distribution of file types in the project."""
        distribution = defaultdict(int)

        for file_path in self.nodes:
            ext = Path(file_path).suffix
            distribution[ext] += 1

        return dict(distribution)

    def _get_most_imported_files(self, limit: int = 5) -> List[Tuple[str, int]]:
        """Get the most imported files (highest in-degree)."""
        files_with_counts = [
            (file_path, self.graph.in_degree(file_path)) for file_path in self.graph.nodes()
        ]

        files_with_counts.sort(key=lambda x: x[1], reverse=True)
        return files_with_counts[:limit]

    def _get_most_dependent_files(self, limit: int = 5) -> List[Tuple[str, int]]:
        """Get files with the most dependencies (highest out-degree)."""
        files_with_counts = [
            (file_path, self.graph.out_degree(file_path)) for file_path in self.graph.nodes()
        ]

        files_with_counts.sort(key=lambda x: x[1], reverse=True)
        return files_with_counts[:limit]

    def get_shortest_path(self, source: str, target: str) -> Optional[List[str]]:
        """Get the shortest dependency path between two files."""
        try:
            path = nx.shortest_path(self.graph, source, target)
            return list(path)  # Ensure we return a list of strings
        except (nx.NetworkXNoPath, nx.NodeNotFound):
            return None

    def is_reachable(self, source: str, target: str) -> bool:
        """Check if target file is reachable from source file."""
        try:
            nx.shortest_path(self.graph, source, target)
            return True
        except (nx.NetworkXNoPath, nx.NodeNotFound):
            return False

    def find_dependency_path(self, source: str, target: str) -> Optional[List[str]]:
        """
        Find a dependency path between two files.

        Args:
            source (str): Source file path
            target (str): Target file path

        Returns:
            Optional[List[str]]: List of file paths forming the dependency path,
                                or None if no path exists

        Examples:
            >>> graph = DependencyGraph("/path/to/project")
            >>> path = graph.find_dependency_path("/src/A.vue", "/src/C.vue")
            >>> if path:
            ...     print(f"Path: {' -> '.join(path)}")
        """
        return self.get_shortest_path(source, target)
