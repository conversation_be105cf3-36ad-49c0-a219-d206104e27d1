"""
Advanced metrics and analysis for dependency graphs.

This module provides sophisticated analysis capabilities including:
- Dependency complexity metrics
- Architectural insights
- Code quality indicators
- Maintainability scores
- Performance impact analysis
"""

from collections import defaultdict
from pathlib import Path
from typing import TYPE_CHECKING, Any, Dict, List, Optional, Tuple

import networkx as nx

from .dependency_analyzer import DependencyNode, DependencyType, ImportType

if TYPE_CHECKING:
    from .dependency_graph import DependencyGraph


class DependencyMetrics:
    """
    Advanced metrics calculator for dependency analysis.

    Provides comprehensive metrics about project structure, complexity,
    and maintainability based on dependency relationships.
    """

    def __init__(self, dependency_graph: "DependencyGraph") -> None:
        """
        Initialize metrics calculator.

        Args:
            dependency_graph: DependencyGraph instance to analyze
        """
        self.graph = dependency_graph
        self.nodes = dependency_graph.nodes
        self.nx_graph = dependency_graph.graph

    def calculate_all_metrics(self) -> Dict[str, Any]:
        """
        Calculate comprehensive metrics for the project.

        Returns:
            Dict[str, Any]: Complete metrics report
        """
        return {
            "basic_metrics": self.get_basic_metrics(),
            "complexity_metrics": self.get_complexity_metrics(),
            "architectural_metrics": self.get_architectural_metrics(),
            "maintainability_metrics": self.get_maintainability_metrics(),
            "quality_metrics": self.get_quality_metrics(),
            "performance_metrics": self.get_performance_metrics(),
            "file_metrics": self.get_file_level_metrics(),
            "framework_metrics": self.get_framework_specific_metrics(),
        }

    def get_basic_metrics(self) -> Dict[str, Any]:
        """
        Calculate basic project metrics from the dependency graph.

        Provides fundamental statistics about the project structure including
        file counts, dependency relationships, and graph topology.

        Returns:
            Dict[str, Any]: Basic metrics including:
                - total_files: Number of analyzed files
                - total_dependencies: Number of dependency relationships
                - average_dependencies_per_file: Mean dependencies per file
                - entry_points: Number of entry point files
                - leaf_nodes: Number of leaf node files
                - isolated_files: Number of files with no connections

        Examples:
            >>> metrics = DependencyMetrics(graph)
            >>> basic = metrics.get_basic_metrics()
            >>> print(f"Project has {basic['total_files']} files")
            >>> print(f"Average dependencies: {basic['average_dependencies_per_file']:.2f}")
        """
        total_files = len(self.nodes)
        total_edges = self.nx_graph.number_of_edges()

        return {
            "total_files": total_files,
            "total_dependencies": total_edges,
            "average_dependencies_per_file": total_edges / total_files if total_files > 0 else 0,
            "entry_points": len([n for n in self.nodes.values() if n.is_entry_point]),
            "leaf_nodes": len([n for n in self.nodes.values() if n.is_leaf_node]),
            "isolated_files": len(list(nx.isolates(self.nx_graph))),
        }

    def get_complexity_metrics(self) -> Dict[str, Any]:
        """Calculate complexity metrics."""
        # Cyclomatic complexity based on graph structure
        cyclomatic_complexity = (
            self.nx_graph.number_of_edges() - self.nx_graph.number_of_nodes() + 1
        )

        # Depth metrics
        depths = []
        for node in self.nx_graph.nodes():
            try:
                # Calculate maximum depth from this node
                lengths = nx.single_source_shortest_path_length(self.nx_graph, node)
                if lengths:
                    depths.append(max(lengths.values()))
            except (nx.NetworkXError, KeyError):
                depths.append(0)

        # Fan-in/Fan-out metrics
        fan_ins = [self.nx_graph.in_degree(node) for node in self.nx_graph.nodes()]
        fan_outs = [self.nx_graph.out_degree(node) for node in self.nx_graph.nodes()]

        return {
            "cyclomatic_complexity": cyclomatic_complexity,
            "max_dependency_depth": max(depths) if depths else 0,
            "average_dependency_depth": sum(depths) / len(depths) if depths else 0,
            "max_fan_in": max(fan_ins) if fan_ins else 0,
            "max_fan_out": max(fan_outs) if fan_outs else 0,
            "average_fan_in": sum(fan_ins) / len(fan_ins) if fan_ins else 0,
            "average_fan_out": sum(fan_outs) / len(fan_outs) if fan_outs else 0,
            "complexity_distribution": self._get_complexity_distribution(),
        }

    def get_architectural_metrics(self) -> Dict[str, Any]:
        """Analyze architectural patterns and structure."""
        # Component analysis
        strongly_connected = list(nx.strongly_connected_components(self.nx_graph))
        weakly_connected = list(nx.weakly_connected_components(self.nx_graph))

        # Modularity analysis
        try:
            modularity = nx.algorithms.community.modularity(
                self.nx_graph.to_undirected(), weakly_connected
            )
        except (nx.NetworkXError, ValueError):
            modularity = 0

        # Centrality measures
        try:
            betweenness = nx.betweenness_centrality(self.nx_graph)
            pagerank = nx.pagerank(self.nx_graph)
        except (nx.NetworkXError, ValueError):
            betweenness = pagerank = {}

        # Identify architectural patterns
        patterns = self._identify_architectural_patterns()

        return {
            "strongly_connected_components": len(strongly_connected),
            "weakly_connected_components": len(weakly_connected),
            "largest_component_size": (
                max(len(c) for c in weakly_connected) if weakly_connected else 0
            ),
            "modularity_score": modularity,
            "most_central_files": self._get_top_files(betweenness, 5),
            "most_important_files": self._get_top_files(pagerank, 5),
            "architectural_patterns": patterns,
            "layering_violations": self._detect_layering_violations(),
        }

    def get_maintainability_metrics(self) -> Dict[str, Any]:
        """Calculate maintainability indicators."""
        # Coupling metrics
        afferent_coupling = {}  # Files that depend on this file
        efferent_coupling = {}  # Files this file depends on

        for node in self.nx_graph.nodes():
            afferent_coupling[node] = self.nx_graph.in_degree(node)
            efferent_coupling[node] = self.nx_graph.out_degree(node)

        # Instability metric (Ce / (Ca + Ce))
        instability = {}
        for node in self.nx_graph.nodes():
            ca = afferent_coupling[node]  # Afferent coupling
            ce = efferent_coupling[node]  # Efferent coupling
            instability[node] = ce / (ca + ce) if (ca + ce) > 0 else 0

        # Change impact analysis
        change_impact = self._calculate_change_impact()

        return {
            "average_instability": (
                sum(instability.values()) / len(instability) if instability else 0
            ),
            "most_unstable_files": self._get_top_files(instability, 5),
            "most_stable_files": self._get_bottom_files(instability, 5),
            "high_coupling_files": self._get_high_coupling_files(),
            "change_impact_scores": change_impact,
            "maintenance_hotspots": self._identify_maintenance_hotspots(),
        }

    def get_quality_metrics(self) -> Dict[str, Any]:
        """Assess code quality indicators."""
        # Import quality metrics
        import_stats = self._analyze_import_patterns()

        # File size and complexity correlation
        file_sizes = {}
        for file_path, node in self.nodes.items():
            try:
                file_sizes[file_path] = Path(file_path).stat().st_size
            except (OSError, FileNotFoundError):
                file_sizes[file_path] = 0

        # Dependency health
        dependency_health = self._assess_dependency_health()

        return {
            "import_quality": import_stats,
            "file_size_distribution": self._get_size_distribution(file_sizes),
            "dependency_health": dependency_health,
            "code_smells": self._detect_code_smells(),
            "best_practices_score": self._calculate_best_practices_score(),
        }

    def get_performance_metrics(self) -> Dict[str, Any]:
        """Analyze performance-related metrics."""
        # Bundle size impact
        bundle_impact = self._estimate_bundle_impact()

        # Loading performance
        loading_metrics = self._analyze_loading_performance()

        return {
            "estimated_bundle_impact": bundle_impact,
            "loading_performance": loading_metrics,
            "lazy_loading_opportunities": self._identify_lazy_loading_opportunities(),
            "tree_shaking_effectiveness": self._assess_tree_shaking(),
        }

    def get_file_level_metrics(self) -> Dict[str, Dict[str, Any]]:
        """Get detailed metrics for each file."""
        file_metrics = {}

        for file_path, node in self.nodes.items():
            file_metrics[file_path] = {
                "import_count": len(node.imports),
                "export_count": len(node.exports),
                "local_dependencies": len(
                    [imp for imp in node.imports if imp.dependency_type == DependencyType.LOCAL]
                ),
                "external_dependencies": len(
                    [imp for imp in node.imports if imp.dependency_type == DependencyType.EXTERNAL]
                ),
                "dependents_count": self.nx_graph.in_degree(file_path),
                "dependencies_count": self.nx_graph.out_degree(file_path),
                "is_entry_point": node.is_entry_point,
                "is_leaf_node": node.is_leaf_node,
                "file_type": Path(file_path).suffix,
                "complexity_score": self._calculate_file_complexity(file_path, node),
            }

        return file_metrics

    def get_framework_specific_metrics(self) -> Dict[str, Any]:
        """Get framework-specific analysis."""
        vue_metrics = self._analyze_vue_patterns()
        react_metrics = self._analyze_react_patterns()

        return {
            "vue_metrics": vue_metrics,
            "react_metrics": react_metrics,
            "framework_distribution": self._get_framework_distribution(),
        }

    def _get_complexity_distribution(self) -> Dict[str, int]:
        """Get distribution of complexity levels."""
        distribution = {"low": 0, "medium": 0, "high": 0, "very_high": 0}

        for node in self.nx_graph.nodes():
            degree = self.nx_graph.degree(node)
            if degree <= 2:
                distribution["low"] += 1
            elif degree <= 5:
                distribution["medium"] += 1
            elif degree <= 10:
                distribution["high"] += 1
            else:
                distribution["very_high"] += 1

        return distribution

    def _get_top_files(self, metric_dict: Dict[str, float], count: int) -> List[Tuple[str, float]]:
        """Get top files by metric value."""
        return sorted(metric_dict.items(), key=lambda x: x[1], reverse=True)[:count]

    def _get_bottom_files(
        self, metric_dict: Dict[str, float], count: int
    ) -> List[Tuple[str, float]]:
        """Get bottom files by metric value."""
        return sorted(metric_dict.items(), key=lambda x: x[1])[:count]

    def _identify_architectural_patterns(self) -> Dict[str, Any]:
        """Identify common architectural patterns."""
        patterns = {
            "layered_architecture": self._detect_layered_architecture(),
            "mvc_pattern": self._detect_mvc_pattern(),
            "component_hierarchy": self._analyze_component_hierarchy(),
            "service_layer": self._detect_service_layer(),
        }
        return patterns

    def _detect_layered_architecture(self) -> Dict[str, Any]:
        """Detect layered architecture patterns."""
        # Analyze directory structure for layers
        layers = defaultdict(list)
        for file_path in self.nodes.keys():
            path_parts = Path(file_path).parts
            if len(path_parts) > 1:
                potential_layer = path_parts[-2]  # Parent directory
                layers[potential_layer].append(file_path)

        # Common layer names
        layer_keywords = ["components", "services", "utils", "models", "views", "controllers"]
        detected_layers = {
            layer: files for layer, files in layers.items() if layer.lower() in layer_keywords
        }

        return {
            "detected_layers": list(detected_layers.keys()),
            "layer_sizes": {layer: len(files) for layer, files in detected_layers.items()},
            "cross_layer_dependencies": self._count_cross_layer_deps(detected_layers),
        }

    def _detect_mvc_pattern(self) -> Dict[str, Any]:
        """Detect MVC pattern usage."""
        mvc_files = {"models": [], "views": [], "controllers": []}

        for file_path in self.nodes.keys():
            file_lower = file_path.lower()
            if "model" in file_lower:
                mvc_files["models"].append(file_path)
            elif "view" in file_lower or "component" in file_lower:
                mvc_files["views"].append(file_path)
            elif "controller" in file_lower or "service" in file_lower:
                mvc_files["controllers"].append(file_path)

        return {
            "mvc_distribution": {k: len(v) for k, v in mvc_files.items()},
            "mvc_adherence_score": self._calculate_mvc_adherence(mvc_files),
        }

    def _analyze_component_hierarchy(self) -> Dict[str, Any]:
        """Analyze component hierarchy depth and structure."""
        component_files = [
            f
            for f in self.nodes.keys()
            if "component" in f.lower()
            or f.endswith(".vue")
            or f.endswith(".jsx")
            or f.endswith(".tsx")
        ]

        if not component_files:
            return {"hierarchy_depth": 0, "component_count": 0}

        # Calculate hierarchy depth
        max_depth = 0
        for component in component_files:
            try:
                depths = nx.single_source_shortest_path_length(self.nx_graph, component)
                component_depths = [d for target, d in depths.items() if target in component_files]
                if component_depths:
                    max_depth = max(max_depth, max(component_depths))
            except (nx.NetworkXError, KeyError, ValueError):
                continue

        return {
            "hierarchy_depth": max_depth,
            "component_count": len(component_files),
            "average_component_dependencies": sum(
                self.nx_graph.out_degree(c) for c in component_files
            )
            / len(component_files),
        }

    def _detect_service_layer(self) -> Dict[str, Any]:
        """Detect service layer pattern."""
        service_files = [
            f for f in self.nodes.keys() if "service" in f.lower() or "api" in f.lower()
        ]

        if not service_files:
            return {"has_service_layer": False}

        # Analyze service usage
        service_usage = {}
        for service in service_files:
            service_usage[service] = self.nx_graph.in_degree(service)

        return {
            "has_service_layer": True,
            "service_count": len(service_files),
            "most_used_services": self._get_top_files(service_usage, 3),
            "service_isolation_score": self._calculate_service_isolation(service_files),
        }

    def _calculate_file_complexity(self, file_path: str, node: DependencyNode) -> float:
        """Calculate complexity score for a single file."""
        # Base complexity from imports/exports
        import_complexity = len(node.imports) * 0.5
        export_complexity = len(node.exports) * 0.3

        # Graph-based complexity
        graph_complexity = (
            self.nx_graph.in_degree(file_path) + self.nx_graph.out_degree(file_path)
        ) * 0.2

        # File type complexity modifier
        file_ext = Path(file_path).suffix
        type_modifier = {"vue": 1.2, ".tsx": 1.1, ".jsx": 1.1, ".ts": 1.0, ".js": 0.9}.get(
            file_ext, 1.0
        )

        return (import_complexity + export_complexity + graph_complexity) * type_modifier

    def _detect_layering_violations(self) -> List[Dict[str, str]]:
        """Detect violations of layered architecture."""
        violations = []
        # Implementation would analyze cross-layer dependencies
        # that violate architectural principles
        return violations

    def _calculate_change_impact(self) -> Dict[str, float]:
        """Calculate potential impact of changes to each file."""
        impact_scores = {}
        for node in self.nx_graph.nodes():
            # Calculate transitive dependencies
            try:
                descendants = nx.descendants(self.nx_graph, node)
                impact_scores[node] = len(descendants)
            except (nx.NetworkXError, KeyError):
                impact_scores[node] = 0
        return impact_scores

    def _identify_maintenance_hotspots(self) -> List[str]:
        """Identify files that are maintenance hotspots."""
        hotspots = []
        for file_path, node in self.nodes.items():
            # High fan-in and fan-out indicates potential hotspot
            fan_in = self.nx_graph.in_degree(file_path)
            fan_out = self.nx_graph.out_degree(file_path)
            if fan_in > 5 and fan_out > 5:
                hotspots.append(file_path)
        return hotspots

    def _analyze_import_patterns(self) -> Dict[str, Any]:
        """Analyze import patterns for quality assessment."""
        patterns = {
            "relative_imports": 0,
            "absolute_imports": 0,
            "external_imports": 0,
            "dynamic_imports": 0,
            "type_only_imports": 0,
        }

        for node in self.nodes.values():
            for imp in node.imports:
                if imp.import_type in [ImportType.DEFAULT, ImportType.NAMED, ImportType.NAMESPACE]:
                    if imp.imported_module.startswith("."):
                        patterns["relative_imports"] += 1
                    else:
                        patterns["absolute_imports"] += 1
                elif imp.import_type == ImportType.DYNAMIC:
                    patterns["dynamic_imports"] += 1
                # Note: TYPE_ONLY is not in the current enum, so we skip it

                if imp.dependency_type == DependencyType.EXTERNAL:
                    patterns["external_imports"] += 1

        return patterns

    def _get_size_distribution(self, file_sizes: Dict[str, int]) -> Dict[str, int]:
        """Get file size distribution."""
        distribution = {"small": 0, "medium": 0, "large": 0, "very_large": 0}

        for size in file_sizes.values():
            if size < 1024:  # < 1KB
                distribution["small"] += 1
            elif size < 10240:  # < 10KB
                distribution["medium"] += 1
            elif size < 102400:  # < 100KB
                distribution["large"] += 1
            else:
                distribution["very_large"] += 1

        return distribution

    def _assess_dependency_health(self) -> Dict[str, Any]:
        """Assess overall dependency health."""
        circular_deps = len(list(nx.simple_cycles(self.nx_graph)))

        return {
            "circular_dependencies": circular_deps,
            "dependency_health_score": max(0, 100 - circular_deps * 10),
            "unused_files": len(list(nx.isolates(self.nx_graph))),
        }

    def _detect_code_smells(self) -> List[Dict[str, Any]]:
        """Detect potential code smells."""
        smells = []

        for file_path, node in self.nodes.items():
            # God object (too many dependencies)
            if len(node.imports) > 20:
                smells.append(
                    {
                        "type": "god_object",
                        "file": file_path,
                        "severity": "high",
                        "description": f"File has {len(node.imports)} imports",
                    }
                )

            # Unused exports
            if len(node.exports) > 0 and self.nx_graph.in_degree(file_path) == 0:
                smells.append(
                    {
                        "type": "unused_exports",
                        "file": file_path,
                        "severity": "medium",
                        "description": "File exports but is not imported",
                    }
                )

        return smells

    def _calculate_best_practices_score(self) -> float:
        """Calculate adherence to best practices."""
        score = 100.0

        # Deduct points for issues
        circular_deps = len(list(nx.simple_cycles(self.nx_graph)))
        score -= circular_deps * 5

        # Deduct for high coupling
        high_coupling_files = len(
            [f for f in self.nx_graph.nodes() if self.nx_graph.degree(f) > 10]
        )
        score -= high_coupling_files * 2

        return max(0, score)

    def _estimate_bundle_impact(self) -> Dict[str, Any]:
        """Estimate bundle size impact."""
        # Simplified estimation based on file count and dependencies
        total_files = len(self.nodes)
        external_deps = sum(
            1
            for node in self.nodes.values()
            for imp in node.imports
            if imp.dependency_type == DependencyType.EXTERNAL
        )

        estimated_size = total_files * 5 + external_deps * 50  # KB estimate

        return {
            "estimated_bundle_size_kb": estimated_size,
            "external_dependency_impact": external_deps * 50,
            "optimization_potential": max(0, estimated_size - total_files * 2),
        }

    def _analyze_loading_performance(self) -> Dict[str, Any]:
        """Analyze loading performance characteristics."""
        entry_points = [f for f, n in self.nodes.items() if n.is_entry_point]

        loading_chains = {}
        for entry in entry_points:
            try:
                chain_length = max(
                    nx.single_source_shortest_path_length(self.nx_graph, entry).values()
                )
                loading_chains[entry] = chain_length
            except (nx.NetworkXError, KeyError, ValueError):
                loading_chains[entry] = 0

        return {
            "max_loading_chain": max(loading_chains.values()) if loading_chains else 0,
            "average_loading_chain": (
                sum(loading_chains.values()) / len(loading_chains) if loading_chains else 0
            ),
            "critical_path_files": self._get_top_files(loading_chains, 3),
        }

    def _identify_lazy_loading_opportunities(self) -> List[str]:
        """Identify files that could benefit from lazy loading."""
        opportunities = []

        for file_path in self.nx_graph.nodes():
            # Files with low in-degree but high out-degree are good candidates
            in_degree = self.nx_graph.in_degree(file_path)
            out_degree = self.nx_graph.out_degree(file_path)

            if in_degree <= 2 and out_degree >= 3:
                opportunities.append(file_path)

        return opportunities

    def _assess_tree_shaking(self) -> Dict[str, Any]:
        """Assess tree-shaking effectiveness."""
        # Analyze export usage
        unused_exports = 0
        total_exports = 0

        for node in self.nodes.values():
            total_exports += len(node.exports)
            # Simplified: assume exports are unused if file has low in-degree
            if self.nx_graph.in_degree(node.file_path) == 0:
                unused_exports += len(node.exports)

        effectiveness = (
            (total_exports - unused_exports) / total_exports if total_exports > 0 else 1.0
        )

        return {
            "tree_shaking_effectiveness": effectiveness,
            "unused_exports_count": unused_exports,
            "total_exports_count": total_exports,
        }

    def _analyze_vue_patterns(self) -> Dict[str, Any]:
        """Analyze Vue.js specific patterns."""
        vue_files = [f for f in self.nodes.keys() if f.endswith(".vue")]

        if not vue_files:
            return {"vue_files_count": 0}

        return {
            "vue_files_count": len(vue_files),
            "average_vue_dependencies": sum(self.nx_graph.out_degree(f) for f in vue_files)
            / len(vue_files),
            "vue_component_depth": self._calculate_vue_component_depth(vue_files),
        }

    def _analyze_react_patterns(self) -> Dict[str, Any]:
        """Analyze React specific patterns."""
        react_files = [f for f in self.nodes.keys() if f.endswith((".jsx", ".tsx"))]

        if not react_files:
            return {"react_files_count": 0}

        return {
            "react_files_count": len(react_files),
            "average_react_dependencies": sum(self.nx_graph.out_degree(f) for f in react_files)
            / len(react_files),
            "react_component_depth": self._calculate_react_component_depth(react_files),
        }

    def _get_framework_distribution(self) -> Dict[str, int]:
        """Get distribution of files by framework."""
        distribution = {"vue": 0, "react": 0, "typescript": 0, "javascript": 0}

        for file_path in self.nodes.keys():
            ext = Path(file_path).suffix
            if ext == ".vue":
                distribution["vue"] += 1
            elif ext in [".jsx", ".tsx"]:
                distribution["react"] += 1
            elif ext == ".ts":
                distribution["typescript"] += 1
            elif ext == ".js":
                distribution["javascript"] += 1

        return distribution

    def _calculate_vue_component_depth(self, vue_files: List[str]) -> int:
        """Calculate maximum Vue component hierarchy depth."""
        max_depth = 0
        for vue_file in vue_files:
            try:
                depths = nx.single_source_shortest_path_length(self.nx_graph, vue_file)
                vue_depths = [d for target, d in depths.items() if target in vue_files]
                if vue_depths:
                    max_depth = max(max_depth, max(vue_depths))
            except (nx.NetworkXError, KeyError, ValueError):
                continue
        return max_depth

    def _calculate_react_component_depth(self, react_files: List[str]) -> int:
        """Calculate maximum React component hierarchy depth."""
        max_depth = 0
        for react_file in react_files:
            try:
                depths = nx.single_source_shortest_path_length(self.nx_graph, react_file)
                react_depths = [d for target, d in depths.items() if target in react_files]
                if react_depths:
                    max_depth = max(max_depth, max(react_depths))
            except (nx.NetworkXError, KeyError, ValueError):
                continue
        return max_depth

    def _get_high_coupling_files(self) -> List[Tuple[str, int]]:
        """Get files with high coupling (high fan-in + fan-out)."""
        coupling_scores = {}
        for node in self.nx_graph.nodes():
            coupling_scores[node] = self.nx_graph.in_degree(node) + self.nx_graph.out_degree(node)

        return self._get_top_files(coupling_scores, 10)

    def _count_cross_layer_deps(self, layers: Dict[str, List[str]]) -> int:
        """Count dependencies that cross architectural layers."""
        cross_layer_count = 0
        layer_map = {}

        # Create reverse mapping
        for layer, files in layers.items():
            for file in files:
                layer_map[file] = layer

        # Count cross-layer dependencies
        for source, target in self.nx_graph.edges():
            source_layer = layer_map.get(source)
            target_layer = layer_map.get(target)

            if source_layer and target_layer and source_layer != target_layer:
                cross_layer_count += 1

        return cross_layer_count

    def _calculate_mvc_adherence(self, mvc_files: Dict[str, List[str]]) -> float:
        """Calculate adherence to MVC pattern."""
        # Simplified scoring based on proper separation
        total_files = sum(len(files) for files in mvc_files.values())
        if total_files == 0:
            return 0.0

        # Check for proper dependencies (controllers -> models, views -> controllers)
        proper_deps = 0
        total_deps = 0

        for source, target in self.nx_graph.edges():
            source_type = self._get_mvc_type(source, mvc_files)
            target_type = self._get_mvc_type(target, mvc_files)

            if source_type and target_type:
                total_deps += 1
                # Proper MVC dependencies
                if (source_type == "controllers" and target_type == "models") or (
                    source_type == "views" and target_type == "controllers"
                ):
                    proper_deps += 1

        return proper_deps / total_deps if total_deps > 0 else 1.0

    def _get_mvc_type(self, file_path: str, mvc_files: Dict[str, List[str]]) -> Optional[str]:
        """Get MVC type for a file."""
        for mvc_type, files in mvc_files.items():
            if file_path in files:
                return mvc_type
        return None

    def _calculate_service_isolation(self, service_files: List[str]) -> float:
        """Calculate how well services are isolated."""
        if not service_files:
            return 1.0

        # Services should primarily be used by other files, not depend on many files
        isolation_scores = []
        for service in service_files:
            in_degree = self.nx_graph.in_degree(service)
            out_degree = self.nx_graph.out_degree(service)

            # Good isolation: high in-degree, low out-degree
            if in_degree + out_degree > 0:
                isolation_score = in_degree / (in_degree + out_degree)
                isolation_scores.append(isolation_score)

        return sum(isolation_scores) / len(isolation_scores) if isolation_scores else 1.0
