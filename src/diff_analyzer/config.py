"""
Configuration management for the dependency analyzer.

This module provides configuration loading, validation, and management
capabilities for project-specific settings, custom rules, and user preferences.
"""

import json
import os
from pathlib import Path
from typing import Any, Dict, List, Optional

import yaml

from .exceptions import ConfigurationError
from .validation import validate_configuration


class Config:
    """
    Configuration manager for the dependency analyzer.

    Supports loading configuration from multiple sources with precedence:
    1. Command line arguments (highest priority)
    2. Environment variables
    3. Project-specific config file (.dependency-analyzer.json/yaml)
    4. User config file (~/.dependency-analyzer.json/yaml)
    5. Default values (lowest priority)
    """

    # Default configuration values
    DEFAULT_CONFIG = {
        "max_workers": None,  # Auto-detect based on CPU count
        "exclude_patterns": ["node_modules", ".git", "dist", "build", ".next", ".nuxt"],
        "supported_extensions": {".vue", ".jsx", ".tsx", ".ts", ".js"},
        "output_format": "text",
        "parallel_processing": True,
        "cache_enabled": True,
        "cache_dir": ".dependency_cache",
        "max_file_size": 10 * 1024 * 1024,  # 10MB
        "verbose": False,
        "show_external_deps": False,
        "show_details": False,
        "find_cycles": True,
        "mermaid_type": "flowchart",
        "custom_parsers": {},
        "analysis_rules": {
            "max_dependency_depth": 50,
            "warn_circular_deps": True,
            "warn_large_files": True,
            "warn_many_deps": 20,
        },
        "output_options": {
            "include_line_numbers": True,
            "include_file_sizes": False,
            "include_timestamps": False,
            "sort_dependencies": True,
        },
    }

    def __init__(self, project_root: Optional[str] = None):
        """
        Initialize configuration manager.

        Args:
            project_root (Optional[str]): Project root directory for finding config files
        """
        self.project_root = Path(project_root) if project_root else Path.cwd()
        self._config = self.DEFAULT_CONFIG.copy()
        self._config_sources: List[str] = []

    def load_config(self, config_file: Optional[str] = None) -> Dict[str, Any]:
        """
        Load configuration from all sources.

        Args:
            config_file (Optional[str]): Explicit config file path

        Returns:
            Dict[str, Any]: Merged configuration

        Raises:
            ConfigurationError: If configuration loading fails
        """
        try:
            # Start with defaults
            config = self.DEFAULT_CONFIG.copy()
            self._config_sources = ["defaults"]

            # Load user config
            user_config = self._load_user_config()
            if user_config:
                config.update(user_config)
                self._config_sources.append("user_config")

            # Load project config
            project_config = self._load_project_config(config_file)
            if project_config:
                config.update(project_config)
                self._config_sources.append("project_config")

            # Load environment variables
            env_config = self._load_env_config()
            if env_config:
                config.update(env_config)
                self._config_sources.append("environment")

            # Validate final configuration
            self._config = validate_configuration(config)

            return self._config

        except Exception as e:
            raise ConfigurationError(f"Failed to load configuration: {e}") from e

    def _load_user_config(self) -> Optional[Dict[str, Any]]:
        """
        Load user-level configuration from home directory.

        Searches for configuration files in the user's home directory in order:
        1. .dependency-analyzer.json
        2. .dependency-analyzer.yaml
        3. .dependency-analyzer.yml

        Returns:
            Optional[Dict[str, Any]]: User configuration if found, None otherwise

        Examples:
            >>> config = Config()
            >>> user_config = config._load_user_config()
            >>> if user_config:
            ...     print(f"Found user config with {len(user_config)} settings")
        """
        home_dir = Path.home()

        for filename in [
            ".dependency-analyzer.json",
            ".dependency-analyzer.yaml",
            ".dependency-analyzer.yml",
        ]:
            config_path = home_dir / filename
            if config_path.exists():
                return self._load_config_file(config_path)

        return None

    def _load_project_config(self, config_file: Optional[str] = None) -> Optional[Dict[str, Any]]:
        """Load project-specific configuration."""
        if config_file:
            # Explicit config file
            config_path = Path(config_file)
            if not config_path.exists():
                raise ConfigurationError(f"Config file not found: {config_file}")
            return self._load_config_file(config_path)

        # Look for config files in project root
        for filename in [
            ".dependency-analyzer.json",
            ".dependency-analyzer.yaml",
            ".dependency-analyzer.yml",
            "dependency-analyzer.config.json",
        ]:
            config_path = self.project_root / filename
            if config_path.exists():
                return self._load_config_file(config_path)

        return None

    def _load_config_file(self, config_path: Path) -> Dict[str, Any]:
        """Load configuration from a specific file."""
        try:
            with open(config_path, "r", encoding="utf-8") as f:
                if config_path.suffix.lower() in [".yaml", ".yml"]:
                    return yaml.safe_load(f) or {}
                else:
                    return json.load(f)
        except Exception as e:
            raise ConfigurationError(f"Failed to load config file {config_path}: {e}")

    def _load_env_config(self) -> Dict[str, Any]:
        """Load configuration from environment variables."""
        env_config = {}

        # Map environment variables to config keys
        env_mappings = {
            "DEPENDENCY_ANALYZER_MAX_WORKERS": ("max_workers", int),
            "DEPENDENCY_ANALYZER_PARALLEL": ("parallel_processing", lambda x: x.lower() == "true"),
            "DEPENDENCY_ANALYZER_VERBOSE": ("verbose", lambda x: x.lower() == "true"),
            "DEPENDENCY_ANALYZER_CACHE": ("cache_enabled", lambda x: x.lower() == "true"),
            "DEPENDENCY_ANALYZER_FORMAT": ("output_format", str),
            "DEPENDENCY_ANALYZER_EXCLUDE": ("exclude_patterns", lambda x: x.split(",")),
        }

        for env_var, (config_key, converter) in env_mappings.items():
            value = os.getenv(env_var)
            if value is not None:
                try:
                    env_config[config_key] = converter(value)
                except Exception as e:
                    raise ConfigurationError(f"Invalid environment variable {env_var}: {e}")

        return env_config

    def get(self, key: str, default: Any = None) -> Any:
        """Get configuration value."""
        return self._config.get(key, default)

    def set(self, key: str, value: Any) -> None:
        """Set configuration value."""
        self._config[key] = value

    def update(self, config_dict: Dict[str, Any]) -> None:
        """Update configuration with dictionary."""
        validated_config = validate_configuration(config_dict)
        self._config.update(validated_config)

    def get_config_sources(self) -> List[str]:
        """Get list of configuration sources that were loaded."""
        return self._config_sources.copy()

    def save_user_config(self, config_path: Optional[str] = None) -> None:
        """Save current configuration to user config file."""
        if config_path:
            save_path = Path(config_path)
        else:
            save_path = Path.home() / ".dependency-analyzer.json"

        try:
            with open(save_path, "w", encoding="utf-8") as f:
                json.dump(self._config, f, indent=2, sort_keys=True)
        except Exception as e:
            raise ConfigurationError(f"Failed to save config to {save_path}: {e}")

    def save_project_config(self, config_path: Optional[str] = None) -> None:
        """Save current configuration to project config file."""
        if config_path:
            save_path = Path(config_path)
        else:
            save_path = self.project_root / ".dependency-analyzer.json"

        try:
            with open(save_path, "w", encoding="utf-8") as f:
                json.dump(self._config, f, indent=2, sort_keys=True)
        except Exception as e:
            raise ConfigurationError(f"Failed to save config to {save_path}: {e}")

    def to_dict(self) -> Dict[str, Any]:
        """Get configuration as dictionary."""
        return self._config.copy()

    def __getitem__(self, key: str) -> Any:
        """Allow dictionary-style access."""
        return self._config[key]

    def __setitem__(self, key: str, value: Any) -> None:
        """Allow dictionary-style assignment."""
        self._config[key] = value

    def __contains__(self, key: str) -> bool:
        """Allow 'in' operator."""
        return key in self._config


def load_config(project_root: Optional[str] = None, config_file: Optional[str] = None) -> Config:
    """
    Convenience function to load configuration.

    Args:
        project_root (Optional[str]): Project root directory
        config_file (Optional[str]): Explicit config file path

    Returns:
        Config: Loaded configuration object
    """
    config = Config(project_root)
    config.load_config(config_file)
    return config
