"""
Vue.js and React Dependency Analyzer

A comprehensive Python tool that uses tree-sitter-typescript to parse Vue.js and React
project source code and extract dependency relationships. This tool provides detailed
analysis of component hierarchies, import/export relationships, and dependency graphs
with multiple output formats.

Key Features:
- Multi-framework support (Vue.js, React, TypeScript)
- Dependency graph analysis and visualization
- Circular dependency detection
- Multiple output formats (JSON, DOT, Mermaid, Text)
- Extensible parser architecture
"""

__version__ = "0.1.0"
__author__ = "Diff Analyzer Team"
__email__ = "<EMAIL>"

from .advanced_formatters import MarkdownReportFormatter, MetricsJSONFormatter
from .config import Config, load_config

# Core classes and functions
from .dependency_analyzer import (
    BaseParser,
    DependencyNode,
    DependencyType,
    ImportInfo,
    ImportType,
)
from .dependency_graph import DependencyGraph
from .exceptions import (
    CircularDependencyError,
    ConfigurationError,
    DependencyAnalyzerError,
    FileAccessError,
    GraphAnalysisError,
    ImportResolutionError,
    OutputFormattingError,
    ParseError,
    ProjectStructureError,
    TreeSitterError,
    ValidationError,
)
from .metrics import DependencyMetrics
from .output_formatters import (
    DOTFormatter,
    JSONFormatter,
    MermaidFormatter,
    OutputFormatter,
    TextTreeFormatter,
)
from .react_parser import ReactParser
from .validation import (
    validate_configuration,
    validate_exclude_patterns,
    validate_file_path,
    validate_max_workers,
    validate_output_format,
    validate_project_path,
    validate_project_structure,
    validate_supported_extensions,
)
from .vue_parser import VueParser

__all__ = [
    # Core classes
    "DependencyGraph",
    "BaseParser",
    "VueParser",
    "ReactParser",
    # Data classes
    "DependencyNode",
    "ImportInfo",
    # Enums
    "ImportType",
    "DependencyType",
    # Output formatters
    "OutputFormatter",
    "JSONFormatter",
    "DOTFormatter",
    "MermaidFormatter",
    "TextTreeFormatter",
    # Exceptions
    "DependencyAnalyzerError",
    "ParseError",
    "ImportResolutionError",
    "CircularDependencyError",
    "FileAccessError",
    "ConfigurationError",
    "GraphAnalysisError",
    "OutputFormattingError",
    "ValidationError",
    "TreeSitterError",
    "ProjectStructureError",
    # Validation functions
    "validate_project_path",
    "validate_exclude_patterns",
    "validate_output_format",
    "validate_file_path",
    "validate_supported_extensions",
    "validate_max_workers",
    "validate_project_structure",
    "validate_configuration",
    # Configuration
    "Config",
    "load_config",
    # Advanced Analysis
    "DependencyMetrics",
    "MetricsJSONFormatter",
    "MarkdownReportFormatter",
]
