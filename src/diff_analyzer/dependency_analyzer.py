"""
Dependency Analyzer for Vue.js and React Projects

This module provides tools to parse Vue.js and React project source code
and extract dependency relationships using tree-sitter-typescript.
"""

import re
from dataclasses import dataclass
from enum import Enum
from pathlib import Path
from typing import Dict, List, Optional, Tuple

import tree_sitter
import tree_sitter_typescript as ts_typescript
from tree_sitter import Language, Parser


class ImportType(Enum):
    """
    Enumeration of different types of import statements found in source code.

    This enum categorizes imports based on their syntax and behavior:
    - DEFAULT: Default imports (import Foo from 'module')
    - NAMED: Named imports (import { foo, bar } from 'module')
    - NAMESPACE: Namespace imports (import * as foo from 'module')
    - DYNAMIC: Dynamic imports (import('module'))
    - REQUIRE: CommonJS require statements (const foo = require('module'))
    """

    DEFAULT = "default"
    NAMED = "named"
    NAMESPACE = "namespace"
    DYNAMIC = "dynamic"
    REQUIRE = "require"


class DependencyType(Enum):
    """
    Enumeration of dependency types based on their location and nature.

    This enum categorizes dependencies to help understand project structure:
    - LOCAL: Relative imports within the project (./component, ../utils)
    - EXTERNAL: Third-party packages from node_modules (vue, react, lodash)
    - BUILTIN: Built-in Node.js modules (fs, path, crypto)
    """

    LOCAL = "local"  # Relative imports within the project
    EXTERNAL = "external"  # node_modules dependencies
    BUILTIN = "builtin"  # Built-in modules


@dataclass
class ImportInfo:
    """
    Detailed information about a single import statement.

    This dataclass captures all relevant information about an import,
    including its type, source, and metadata for dependency analysis.

    Attributes:
        source_file (str): Absolute path to the file containing the import
        imported_module (str): The module being imported (resolved path or package name)
        import_type (ImportType): Type of import syntax used
        dependency_type (DependencyType): Category of the dependency
        imported_names (List[str]): List of imported identifiers (for named imports)
        line_number (int): Line number where the import appears
        is_type_only (bool): Whether this is a TypeScript type-only import

    Examples:
        >>> # For: import { createApp } from 'vue'
        >>> ImportInfo(
        ...     source_file="/project/src/main.ts",
        ...     imported_module="vue",
        ...     import_type=ImportType.NAMED,
        ...     dependency_type=DependencyType.EXTERNAL,
        ...     imported_names=["createApp"],
        ...     line_number=1,
        ...     is_type_only=False
        ... )
    """

    source_file: str
    imported_module: str
    import_type: ImportType
    dependency_type: DependencyType
    imported_names: List[str]
    line_number: int
    is_type_only: bool = False


@dataclass
class DependencyNode:
    """
    Represents a single file node in the dependency graph.

    This dataclass contains all information about a file's dependencies,
    exports, and its role in the project structure.

    Attributes:
        file_path (str): Absolute path to the file
        imports (List[ImportInfo]): List of all imports in the file
        exports (List[str]): List of exported identifiers
        is_entry_point (bool): Whether this file is an application entry point
        is_leaf_node (bool): Whether this file has no dependencies

    Examples:
        >>> # For a Vue component file
        >>> DependencyNode(
        ...     file_path="/project/src/components/Button.vue",
        ...     imports=[...],  # List of ImportInfo objects
        ...     exports=["Button"],
        ...     is_entry_point=False,
        ...     is_leaf_node=True
        ... )
    """

    file_path: str
    imports: List[ImportInfo]
    exports: List[str]
    is_entry_point: bool = False
    is_leaf_node: bool = False


class BaseParser:
    """
    Base class for parsing different file types using tree-sitter.

    This abstract base class provides common functionality for parsing
    TypeScript, JavaScript, Vue, and React files. It uses tree-sitter
    for accurate syntax parsing and provides methods for extracting
    imports, exports, and dependency relationships.

    Attributes:
        ts_language: Tree-sitter TypeScript language instance
        tsx_language: Tree-sitter TSX language instance
        ts_parser: Parser for TypeScript/JavaScript files
        tsx_parser: Parser for TSX/JSX files
    """

    def __init__(self):
        """
        Initialize the parser with tree-sitter language support.

        Sets up TypeScript and TSX parsers for handling different
        file types in Vue.js and React projects.
        """
        # Initialize tree-sitter parsers
        self.ts_language = Language(ts_typescript.language_typescript())
        self.tsx_language = Language(ts_typescript.language_tsx())

        self.ts_parser = Parser(self.ts_language)
        self.tsx_parser = Parser(self.tsx_language)

    def parse_file(self, file_path: str) -> Optional[DependencyNode]:
        """
        Parse a file and extract dependency information.

        This is an abstract method that must be implemented by subclasses
        to handle specific file types (Vue, React, etc.).

        Args:
            file_path (str): Absolute path to the file to parse

        Returns:
            Optional[DependencyNode]: Parsed dependency information or None if parsing fails

        Raises:
            NotImplementedError: This method must be implemented by subclasses
        """
        raise NotImplementedError("Subclasses must implement parse_file")

    def _read_file_content(self, file_path: str, max_size: int = 10 * 1024 * 1024) -> str:
        """
        Read file content with proper encoding handling and size limits.

        Attempts to read the file with UTF-8 encoding first, falling back
        to latin-1 if that fails. Returns empty string if file cannot be read.
        Includes file size limits to prevent memory issues with very large files.

        Args:
            file_path (str): Path to the file to read
            max_size (int): Maximum file size in bytes (default: 10MB)

        Returns:
            str: File content or empty string if reading fails

        Examples:
            >>> parser = BaseParser()
            >>> content = parser._read_file_content("/path/to/file.ts")
            >>> print(len(content))
            1234
        """
        try:
            # Check file size first
            file_size = Path(file_path).stat().st_size
            if file_size > max_size:
                print(
                    f"Warning: Skipping large file {file_path} "
                    f"({file_size} bytes > {max_size} bytes)"
                )
                return ""

            with open(file_path, "r", encoding="utf-8") as f:
                return f.read()
        except (UnicodeDecodeError, FileNotFoundError, PermissionError):
            try:
                # Fallback to latin-1 if utf-8 fails
                with open(file_path, "r", encoding="latin-1") as f:
                    return f.read()
            except Exception:
                return ""

    def _get_parser_for_file(self, file_path: str) -> Parser:
        """
        Get the appropriate tree-sitter parser based on file extension.

        Determines whether to use the TypeScript or TSX parser based on
        the file extension. TSX parser is used for .tsx and .jsx files,
        while TypeScript parser is used for .ts and .js files.

        Args:
            file_path (str): Path to the file

        Returns:
            Parser: Appropriate tree-sitter parser instance

        Examples:
            >>> parser = BaseParser()
            >>> ts_parser = parser._get_parser_for_file("component.ts")
            >>> tsx_parser = parser._get_parser_for_file("component.tsx")
        """
        ext = Path(file_path).suffix.lower()
        if ext in [".tsx", ".jsx"]:
            return self.tsx_parser
        else:
            return self.ts_parser

    def _resolve_import_path(
        self, import_path: str, source_file: str
    ) -> Tuple[str, DependencyType]:
        """
        Resolve import path and determine dependency type.

        Converts relative import paths to absolute paths and categorizes
        dependencies as local, external, or built-in based on the import path.

        Args:
            import_path (str): The import path from the source code
            source_file (str): Absolute path to the file containing the import

        Returns:
            Tuple[str, DependencyType]: Resolved path and dependency type

        Examples:
            >>> parser = BaseParser()
            >>> path, dep_type = parser._resolve_import_path("./Component.vue", "/src/main.ts")
            >>> print(path)  # "/src/Component.vue"
            >>> print(dep_type)  # DependencyType.LOCAL

            >>> path, dep_type = parser._resolve_import_path("vue", "/src/main.ts")
            >>> print(path)  # "vue"
            >>> print(dep_type)  # DependencyType.EXTERNAL
        """
        # Remove quotes from import path - handles both single and double quotes
        clean_path = import_path.strip("'\"")

        # Determine dependency type based on import path patterns
        if clean_path.startswith("."):
            # Relative import (./component, ../utils) - always local dependency
            source_dir = Path(source_file).parent
            resolved_path = (source_dir / clean_path).resolve()
            return str(resolved_path), DependencyType.LOCAL
        elif clean_path.startswith("/"):
            # Absolute path from filesystem root - treat as local
            # This is rare but can occur in some build configurations
            return clean_path, DependencyType.LOCAL
        else:
            # Handle complex import resolution for non-relative paths
            # This covers cases like:
            # - Framework-specific absolute imports (src/types/User)
            # - Alias imports (@/components/Button)
            # - External packages (vue, react, lodash)

            # Handle @/ alias imports (common in Vue.js projects)
            if clean_path.startswith("@/"):
                source_project_root = self._find_project_root(source_file)
                if source_project_root:
                    # Replace @/ with src/ (common convention)
                    relative_path = clean_path[2:]  # Remove @/
                    resolved_path = source_project_root / "src" / relative_path
                    return str(resolved_path), DependencyType.LOCAL
                else:
                    # Fallback: return the resolved path without @/
                    return clean_path.replace("@/", "/test/src/"), DependencyType.LOCAL

            if "/" in clean_path and not clean_path.startswith("@"):
                # Potential project-relative path (e.g., 'src/types/User')
                # First, try to find project root by walking up the directory tree
                project_root = Path(source_file)
                while project_root.parent != project_root:
                    project_root = project_root.parent
                    # Check if the path exists with common file extensions
                    potential_file = project_root / clean_path
                    if potential_file.exists() or any(
                        potential_file.with_suffix(ext).exists()
                        for ext in [".ts", ".js", ".vue", ".tsx", ".jsx"]
                    ):
                        return clean_path, DependencyType.LOCAL

                # Fallback: use project root detection for more accurate resolution
                source_project_root = self._find_project_root(source_file)
                if source_project_root:
                    potential_path = source_project_root / clean_path
                    # Check both exact path and with common extensions
                    if potential_path.exists() or any(
                        potential_path.with_suffix(ext).exists()
                        for ext in [".ts", ".js", ".vue", ".tsx", ".jsx"]
                    ):
                        return clean_path, DependencyType.LOCAL

            # Check for built-in Node.js modules
            builtin_modules = {
                "fs",
                "path",
                "os",
                "crypto",
                "util",
                "events",
                "stream",
                "http",
                "https",
                "url",
                "querystring",
                "buffer",
                "child_process",
            }
            if clean_path in builtin_modules:
                return clean_path, DependencyType.BUILTIN

            # Default to external dependency (node_modules package)
            # This includes scoped packages (@vue/reactivity) and regular packages (lodash)
            return clean_path, DependencyType.EXTERNAL

    def _find_project_root(self, file_path: str) -> Optional[Path]:
        """Find the project root by looking for common project files."""
        current = Path(file_path).parent

        while current.parent != current:
            # Look for common project root indicators
            if any(
                (current / indicator).exists()
                for indicator in ["package.json", "tsconfig.json", "vue.config.js", "src"]
            ):
                return current
            current = current.parent

        return None

    def _extract_imports_from_tree(
        self, tree: tree_sitter.Tree, source_code: str, source_file: str
    ) -> List[ImportInfo]:
        """Extract import statements from a tree-sitter parse tree."""
        imports = []

        # Walk the tree to find import statements
        self._walk_tree_for_imports(tree.root_node, source_code, source_file, imports)

        return imports

    def _walk_tree_for_imports(
        self, node: tree_sitter.Node, source_code: str, source_file: str, imports: List[ImportInfo]
    ):
        """Walk the tree recursively to find import statements."""
        if node.type == "import_statement":
            self._process_import_statement(node, source_code, source_file, imports)
        elif node.type == "call_expression":
            # Check for require() or import() calls
            if node.children and len(node.children) >= 2:
                func_node = node.children[0]
                if func_node.type == "identifier":
                    func_name = source_code[func_node.start_byte : func_node.end_byte]
                    if func_name == "require":
                        self._process_require_statement(node, source_code, source_file, imports)
                elif func_node.type == "import":
                    self._process_dynamic_import(node, source_code, source_file, imports)

        # Recursively process children
        for child in node.children:
            self._walk_tree_for_imports(child, source_code, source_file, imports)

    def _process_import_statement(
        self, node: tree_sitter.Node, source_code: str, source_file: str, imports: List[ImportInfo]
    ):
        """Process ES6 import statements."""
        # Extract source module
        source_node = None
        import_clause_node = None

        for child in node.children:
            if child.type == "string":
                source_node = child
            elif child.type == "import_clause":
                import_clause_node = child

        if not source_node:
            return

        source_text = source_code[source_node.start_byte : source_node.end_byte]
        resolved_path, dep_type = self._resolve_import_path(source_text, source_file)

        # Extract imported names and determine import type
        imported_names = []
        import_type = ImportType.DEFAULT

        if import_clause_node:
            imported_names, import_type = self._extract_import_names(
                import_clause_node, source_code
            )

        imports.append(
            ImportInfo(
                source_file=source_file,
                imported_module=resolved_path,
                import_type=import_type,
                dependency_type=dep_type,
                imported_names=imported_names,
                line_number=node.start_point[0] + 1,
            )
        )

    def _process_require_statement(
        self, node: tree_sitter.Node, source_code: str, source_file: str, imports: List[ImportInfo]
    ):
        """Process CommonJS require statements."""
        # Find the require source
        for child in node.children:
            if child.type == "arguments":
                for arg in child.children:
                    if arg.type == "string":
                        source_text = source_code[arg.start_byte : arg.end_byte]
                        resolved_path, dep_type = self._resolve_import_path(
                            source_text, source_file
                        )

                        imports.append(
                            ImportInfo(
                                source_file=source_file,
                                imported_module=resolved_path,
                                import_type=ImportType.REQUIRE,
                                dependency_type=dep_type,
                                imported_names=[],
                                line_number=node.start_point[0] + 1,
                            )
                        )
                        break

    def _process_dynamic_import(
        self, node: tree_sitter.Node, source_code: str, source_file: str, imports: List[ImportInfo]
    ):
        """Process dynamic import() statements."""
        # Find the import source
        for child in node.children:
            if child.type == "arguments":
                for arg in child.children:
                    if arg.type == "string":
                        source_text = source_code[arg.start_byte : arg.end_byte]
                        resolved_path, dep_type = self._resolve_import_path(
                            source_text, source_file
                        )

                        imports.append(
                            ImportInfo(
                                source_file=source_file,
                                imported_module=resolved_path,
                                import_type=ImportType.DYNAMIC,
                                dependency_type=dep_type,
                                imported_names=[],
                                line_number=node.start_point[0] + 1,
                            )
                        )
                        break

    def _extract_import_names(
        self, import_clause_node: tree_sitter.Node, source_code: str
    ) -> Tuple[List[str], ImportType]:
        """Extract imported names from import clause."""
        imported_names = []
        import_type = ImportType.DEFAULT

        for child in import_clause_node.children:
            if child.type == "identifier":
                # Default import
                name = source_code[child.start_byte : child.end_byte]
                imported_names.append(name)
                import_type = ImportType.DEFAULT
            elif child.type == "named_imports":
                # Named imports
                import_type = ImportType.NAMED
                for named_child in child.children:
                    if named_child.type == "import_specifier":
                        for spec_child in named_child.children:
                            if spec_child.type == "identifier":
                                name = source_code[spec_child.start_byte : spec_child.end_byte]
                                imported_names.append(name)
            elif child.type == "namespace_import":
                # Namespace import (import * as name)
                import_type = ImportType.NAMESPACE
                for ns_child in child.children:
                    if ns_child.type == "identifier":
                        name = source_code[ns_child.start_byte : ns_child.end_byte]
                        imported_names.append(name)

        return imported_names, import_type

    def _is_entry_point(self, file_path: str) -> bool:
        """
        Determine if a file is an entry point.

        Entry points are typically main application files that serve as the
        starting point for the application or major modules.

        Args:
            file_path (str): Path to the file to check

        Returns:
            bool: True if the file is likely an entry point

        Examples:
            >>> parser = BaseParser()
            >>> parser._is_entry_point("/src/main.ts")
            True
            >>> parser._is_entry_point("/src/components/Button.vue")
            False
        """
        file_name = Path(file_path).name.lower()

        # Common entry point file names
        entry_point_names = {
            "main.ts",
            "main.js",
            "main.tsx",
            "main.jsx",
            "index.ts",
            "index.js",
            "index.tsx",
            "index.jsx",
            "app.ts",
            "app.js",
            "app.tsx",
            "app.jsx",
            "app.vue",
        }

        return file_name in entry_point_names

    def _clean_import_path(self, import_path: str) -> str:
        """
        Clean import path by removing quotes and normalizing.

        Args:
            import_path (str): Raw import path that may contain quotes

        Returns:
            str: Cleaned import path without quotes

        Examples:
            >>> parser = BaseParser()
            >>> parser._clean_import_path('"./Component.vue"')
            './Component.vue'
            >>> parser._clean_import_path("'./utils'")
            './utils'
        """
        return import_path.strip("'\"")

    def _extract_exports_from_tree(self, tree: tree_sitter.Tree, source_code: str) -> List[str]:
        """
        Extract export statements from a tree-sitter parse tree.

        Args:
            tree (tree_sitter.Tree): Parsed tree from tree-sitter
            source_code (str): Original source code

        Returns:
            List[str]: List of exported names

        Examples:
            >>> parser = BaseParser()
            >>> tree = parser.ts_parser.parse(b'export const helper = () => {};')
            >>> exports = parser._extract_exports_from_tree(tree, 'export const helper = () => {};')
            >>> 'helper' in exports
            True
        """
        exports = []

        # Use regex patterns as a fallback for more reliable extraction
        # This is simpler and more reliable than tree-sitter for this use case

        # Pattern for export const/let/var name = ...
        const_exports = re.findall(r"export\s+(?:const|let|var)\s+(\w+)\s*[=:]", source_code)
        exports.extend(const_exports)

        # Pattern for export function name() {...}
        function_exports = re.findall(r"export\s+function\s+(\w+)", source_code)
        exports.extend(function_exports)

        # Pattern for export class Name {...}
        class_exports = re.findall(r"export\s+class\s+(\w+)", source_code)
        exports.extend(class_exports)

        # Pattern for export default (we'll use a generic name)
        default_matches = re.findall(r"export\s+default\s+(\w+)", source_code)
        exports.extend(default_matches)

        # If we found export default but no name, add a generic default
        if re.search(r"export\s+default", source_code) and not default_matches:
            exports.append("default")

        return exports

    def _process_export_statement(
        self, node: tree_sitter.Node, source_code: str, exports: List[str]
    ):
        """Process export statements to extract exported names."""
        for child in node.children:
            if child.type == "lexical_declaration":
                self._process_export_declaration(child, source_code, exports)
            elif child.type == "function_declaration" or child.type == "class_declaration":
                # Extract function or class name
                for grandchild in child.children:
                    if grandchild.type == "identifier":
                        name = source_code[grandchild.start_byte : grandchild.end_byte]
                        exports.append(name)
                        break
            elif child.type == "export_clause":
                # Handle named exports like { utils }
                self._process_export_clause(child, source_code, exports)
            elif child.type == "variable_statement":
                # Handle export const/let/var
                self._process_export_declaration(child, source_code, exports)

    def _process_export_declaration(
        self, node: tree_sitter.Node, source_code: str, exports: List[str]
    ):
        """Process export declarations (const, let, var)."""

        def extract_from_declarator(declarator_node):
            for child in declarator_node.children:
                if child.type == "identifier":
                    name = source_code[child.start_byte : child.end_byte]
                    exports.append(name)
                    return

        for child in node.children:
            if child.type == "variable_declarator":
                extract_from_declarator(child)
            elif child.type == "variable_declaration":
                # Handle nested variable declarations
                for grandchild in child.children:
                    if grandchild.type == "variable_declarator":
                        extract_from_declarator(grandchild)

    def _process_export_clause(self, node: tree_sitter.Node, source_code: str, exports: List[str]):
        """Process export clauses to extract named exports."""
        for child in node.children:
            if child.type == "export_specifier":
                for grandchild in child.children:
                    if grandchild.type == "identifier":
                        name = source_code[grandchild.start_byte : grandchild.end_byte]
                        exports.append(name)
                        break

    def _node_to_attributes(self, node: DependencyNode) -> Dict:
        """
        Convert a DependencyNode to graph node attributes.

        Args:
            node (DependencyNode): The dependency node to convert

        Returns:
            Dict: Dictionary of attributes for the graph node

        Examples:
            >>> parser = BaseParser()
            >>> node = DependencyNode(file_path="/test/App.vue", imports=[], exports=["App"],
            ...                       is_entry_point=True, is_leaf_node=False)
            >>> attrs = parser._node_to_attributes(node)
            >>> attrs["is_entry_point"]
            True
        """
        return {
            "imports_count": len(node.imports),
            "exports_count": len(node.exports),
            "exports": node.exports,
            "import_count": len(node.imports),
            "is_entry_point": node.is_entry_point,
            "is_leaf_node": node.is_leaf_node,
            "file_type": Path(node.file_path).suffix,
            "local_imports": len(
                [imp for imp in node.imports if imp.dependency_type == DependencyType.LOCAL]
            ),
            "external_imports": len(
                [imp for imp in node.imports if imp.dependency_type == DependencyType.EXTERNAL]
            ),
        }
