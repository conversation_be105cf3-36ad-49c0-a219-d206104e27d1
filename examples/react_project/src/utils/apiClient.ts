// Simple API client implementation
interface ApiResponse<T = any> {
  data: T
  status: number
}

class ApiClient {
  private baseURL: string

  constructor(baseURL: string = '/api') {
    this.baseURL = baseURL
  }

  async get<T>(endpoint: string): Promise<ApiResponse<T>> {
    const response = await fetch(`${this.baseURL}${endpoint}`)
    const data = await response.json()
    return { data, status: response.status }
  }

  async post<T>(endpoint: string, body: any): Promise<ApiResponse<T>> {
    const response = await fetch(`${this.baseURL}${endpoint}`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(body)
    })
    const data = await response.json()
    return { data, status: response.status }
  }

  async put<T>(endpoint: string, body: any): Promise<ApiResponse<T>> {
    const response = await fetch(`${this.baseURL}${endpoint}`, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(body)
    })
    const data = await response.json()
    return { data, status: response.status }
  }

  async delete(endpoint: string): Promise<ApiResponse<void>> {
    const response = await fetch(`${this.baseURL}${endpoint}`, {
      method: 'DELETE'
    })
    return { data: undefined, status: response.status }
  }
}

export const apiClient = new ApiClient()
