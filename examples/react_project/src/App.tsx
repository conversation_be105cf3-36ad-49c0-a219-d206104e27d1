import React from 'react'
import { UserProvider } from './components/UserProvider'
import UserProfile from './components/UserProfile'
import Navigation from './components/Navigation'
import { useUser } from './hooks/useUser'
import './App.css'

function App() {
  return (
    <UserProvider>
      <div className="App">
        <Navigation />
        <main>
          <AppContent />
        </main>
      </div>
    </UserProvider>
  )
}

function AppContent() {
  const { user, loading } = useUser()

  if (loading) {
    return <div>Loading...</div>
  }

  return (
    <div>
      <h1>React Dependency Analysis Demo</h1>
      {user && <UserProfile user={user} />}
    </div>
  )
}

export default App
