import React from 'react'
import { useUser } from '../hooks/useUser'

function Navigation() {
  const { user, refreshUser } = useUser()

  return (
    <nav className="navigation">
      <div className="nav-brand">
        <h1>React App</h1>
      </div>
      <div className="nav-actions">
        {user && <span>Welcome, {user.name}!</span>}
        <button onClick={refreshUser}>Refresh User</button>
      </div>
    </nav>
  )
}

export default Navigation
