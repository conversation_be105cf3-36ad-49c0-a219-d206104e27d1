import React from 'react'

interface UserAvatarProps {
  src: string
  alt?: string
  size?: number
}

function UserAvatar({ src, alt = 'User Avatar', size = 48 }: UserAvatarProps) {
  return (
    <img
      src={src}
      alt={alt}
      width={size}
      height={size}
      className="user-avatar"
      style={{
        borderRadius: '50%',
        objectFit: 'cover'
      }}
    />
  )
}

export default UserAvatar
