import React from 'react'
import { User } from '../types/User'
import UserAvatar from './UserAvatar'
import ContactInfo from './ContactInfo'

interface UserProfileProps {
  user: User
}

function UserProfile({ user }: UserProfileProps) {
  return (
    <div className="user-profile">
      <div className="user-header">
        <UserAvatar src={user.avatar} alt={user.name} size={64} />
        <div className="user-info">
          <h2>{user.name}</h2>
          <p>{user.email}</p>
        </div>
      </div>
      <ContactInfo contact={user.contact} />
    </div>
  )
}

export default UserProfile
