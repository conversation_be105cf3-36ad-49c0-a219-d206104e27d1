import React from 'react'
import { SocialLink } from '../types/User'

interface SocialLinksProps {
  links: SocialLink[]
}

function SocialLinks({ links }: SocialLinksProps) {
  if (!links || links.length === 0) {
    return null
  }

  return (
    <div className="social-links">
      {links.map((link) => (
        <a
          key={link.platform}
          href={link.url}
          target="_blank"
          rel="noopener noreferrer"
          className="social-link"
        >
          {link.platform}
        </a>
      ))}
    </div>
  )
}

export default SocialLinks
