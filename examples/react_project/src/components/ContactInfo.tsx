import React from 'react'
import { Contact } from '../types/User'
import SocialLinks from './SocialLinks'

interface ContactInfoProps {
  contact: Contact
}

function ContactInfo({ contact }: ContactInfoProps) {
  return (
    <div className="contact-info">
      <h3>Contact Information</h3>
      {contact.phone && <p>Phone: {contact.phone}</p>}
      {contact.address && <p>Address: {contact.address}</p>}
      <SocialLinks links={contact.social} />
    </div>
  )
}

export default ContactInfo
