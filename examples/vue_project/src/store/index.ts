import { createStore } from 'vuex'
import { User } from '../types/User'
import { userService } from '../utils/userService'

interface State {
  user: User | null
  loading: boolean
}

export const store = createStore<State>({
  state: {
    user: null,
    loading: false
  },
  mutations: {
    SET_USER(state, user: User) {
      state.user = user
    },
    SET_LOADING(state, loading: boolean) {
      state.loading = loading
    }
  },
  actions: {
    async fetchUser({ commit }, userId: number) {
      commit('SET_LOADING', true)
      try {
        const user = await userService.getUser(userId)
        commit('SET_USER', user)
      } catch (error) {
        console.error('Failed to fetch user:', error)
      } finally {
        commit('SET_LOADING', false)
      }
    }
  }
})

export function useStore() {
  return store
}
