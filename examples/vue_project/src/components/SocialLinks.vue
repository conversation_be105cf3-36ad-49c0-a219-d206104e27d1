<template>
  <div class="social-links">
    <a 
      v-for="link in links" 
      :key="link.platform"
      :href="link.url"
      target="_blank"
      class="social-link"
    >
      {{ link.platform }}
    </a>
  </div>
</template>

<script lang="ts">
import { defineComponent, PropType } from 'vue'
import { SocialLink } from '../types/User'

export default defineComponent({
  name: 'SocialLinks',
  props: {
    links: {
      type: Array as PropType<SocialLink[]>,
      default: () => []
    }
  }
})
</script>

<style scoped>
.social-links {
  display: flex;
  gap: 8px;
}

.social-link {
  padding: 4px 8px;
  background: #f0f0f0;
  text-decoration: none;
  border-radius: 4px;
}
</style>
