<template>
  <div class="user-profile">
    <h2>{{ user.name }}</h2>
    <p>{{ user.email }}</p>
    <UserAvatar :src="user.avatar" :size="64" />
    <ContactInfo :contact="user.contact" />
  </div>
</template>

<script lang="ts">
import { defineComponent, PropType } from 'vue'
import UserAvatar from './UserAvatar.vue'
import ContactInfo from './ContactInfo.vue'
import { User } from '../types/User'

export default defineComponent({
  name: 'UserProfile',
  components: {
    UserAvatar,
    ContactInfo
  },
  props: {
    user: {
      type: Object as PropType<User>,
      required: true
    }
  }
})
</script>

<style scoped>
.user-profile {
  padding: 20px;
  border: 1px solid #ddd;
  border-radius: 8px;
}
</style>
