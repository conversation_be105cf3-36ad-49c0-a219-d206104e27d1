<template>
  <img 
    :src="src" 
    :alt="alt"
    :width="size"
    :height="size"
    class="user-avatar"
  />
</template>

<script lang="ts">
import { defineComponent } from 'vue'

export default defineComponent({
  name: 'UserAvatar',
  props: {
    src: {
      type: String,
      required: true
    },
    alt: {
      type: String,
      default: 'User Avatar'
    },
    size: {
      type: Number,
      default: 48
    }
  }
})
</script>

<style scoped>
.user-avatar {
  border-radius: 50%;
  object-fit: cover;
}
</style>
