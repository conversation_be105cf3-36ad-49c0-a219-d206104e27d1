<template>
  <div class="home">
    <h1>Welcome Home</h1>
    <UserProfile v-if="user" :user="user" />
    <button @click="loadUser">Load User</button>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref } from 'vue'
import UserProfile from '../components/UserProfile.vue'
import { User } from '../types/User'
import { userService } from '../utils/userService'

export default defineComponent({
  name: 'Home',
  components: {
    UserProfile
  },
  setup() {
    const user = ref<User | null>(null)

    const loadUser = async () => {
      try {
        user.value = await userService.getUser(1)
      } catch (error) {
        console.error('Failed to load user:', error)
      }
    }

    return {
      user,
      loadUser
    }
  }
})
</script>

<style scoped>
.home {
  padding: 20px;
}
</style>
