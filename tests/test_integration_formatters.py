"""
Unit tests for the integration formatters module.

Tests the various integration formatters (CSV, XML, SARIF, GitHub Actions)
and their ability to generate output for different development workflows.
"""

import csv
import io
import json
import tempfile
import xml.etree.ElementTree as ET
from pathlib import Path
from unittest.mock import Mock

import pytest

from src.diff_analyzer.dependency_analyzer import DependencyNode, DependencyType, ImportInfo, ImportType
from src.diff_analyzer.dependency_graph import DependencyGraph
from src.diff_analyzer.integration_formatters import (
    CSVFormatter,
    GitHubActionsFormatter,
    SARIFFormatter,
    XMLFormatter,
)


class TestCSVFormatter:
    """Test the CSVFormatter class."""

    def setup_method(self) -> None:
        """Set up test fixtures."""
        self.temp_dir = Path(tempfile.mkdtemp())
        
        # Create a mock dependency graph
        self.mock_graph = Mock(spec=DependencyGraph)
        self.mock_graph.project_root = self.temp_dir
        
        # Create sample nodes
        self.nodes = {
            "/src/main.ts": DependencyNode(
                file_path="/src/main.ts",
                imports=[
                    ImportInfo(
                        source_file="/src/main.ts",
                        imported_module="/src/App.vue",
                        import_type=ImportType.DEFAULT,
                        dependency_type=DependencyType.LOCAL,
                        imported_names=["App"],
                        line_number=1
                    )
                ],
                exports=["main"],
                is_entry_point=True,
                is_leaf_node=False
            ),
            "/src/App.vue": DependencyNode(
                file_path="/src/App.vue",
                imports=[],
                exports=["App"],
                is_entry_point=False,
                is_leaf_node=True
            )
        }
        
        self.mock_graph.nodes = self.nodes
        
        # Mock NetworkX graph
        import networkx as nx
        self.nx_graph = nx.DiGraph()
        self.nx_graph.add_edge("/src/main.ts", "/src/App.vue")
        self.mock_graph.graph = self.nx_graph
        
        # Mock statistics
        self.mock_graph.get_statistics.return_value = {
            "total_files": 2,
            "total_dependencies": 1,
            "entry_points": 1,
            "leaf_nodes": 1
        }

    def teardown_method(self) -> None:
        """Clean up test fixtures."""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)

    def test_csv_formatter_initialization(self) -> None:
        """Test CSVFormatter initialization."""
        formatter = CSVFormatter(self.mock_graph)
        assert formatter.graph == self.mock_graph

    def test_csv_dependencies_format(self) -> None:
        """Test CSV dependencies format."""
        formatter = CSVFormatter(self.mock_graph)
        result = formatter.format("dependencies")

        # Should be valid CSV
        reader = csv.reader(io.StringIO(result))
        rows = list(reader)

        # Should have header row
        assert len(rows) >= 1
        header = rows[0]
        assert "Source File" in header
        assert "Target File" in header

    def test_csv_files_format(self) -> None:
        """Test CSV files format."""
        formatter = CSVFormatter(self.mock_graph)
        result = formatter.format("files")

        reader = csv.reader(io.StringIO(result))
        rows = list(reader)

        # Should have header and data rows
        assert len(rows) >= 2  # Header + at least one data row
        header = rows[0]
        assert "File Path" in header
        assert "Is Entry Point" in header

    def test_csv_metrics_format(self) -> None:
        """Test CSV metrics format."""
        # Mock the get_advanced_metrics method
        self.mock_graph.get_advanced_metrics.return_value = {
            "basic_metrics": {"total_files": 2, "total_dependencies": 1},
            "complexity_metrics": {"cyclomatic_complexity": 1}
        }

        formatter = CSVFormatter(self.mock_graph)
        result = formatter.format("metrics")

        reader = csv.reader(io.StringIO(result))
        rows = list(reader)

        assert len(rows) >= 1
        # Should contain metrics data

    def test_csv_invalid_format_type(self) -> None:
        """Test CSV formatter with invalid format type."""
        formatter = CSVFormatter(self.mock_graph)
        
        with pytest.raises(ValueError, match="Unsupported CSV format type"):
            formatter.format("invalid_type")


class TestXMLFormatter:
    """Test the XMLFormatter class."""

    def setup_method(self) -> None:
        """Set up test fixtures."""
        self.temp_dir = Path(tempfile.mkdtemp())
        self.mock_graph = Mock(spec=DependencyGraph)
        self.mock_graph.project_root = self.temp_dir

        # Mock project info
        self.mock_graph.project_info = {
            "root": str(self.temp_dir),
            "has_package_json": True,
            "has_src_dir": True
        }

        # Mock basic data
        self.mock_graph.get_statistics.return_value = {
            "total_files": 2,
            "total_dependencies": 1
        }

        self.mock_graph.nodes = {
            "/src/test.vue": DependencyNode(
                file_path="/src/test.vue",
                imports=[],
                exports=["Test"],
                is_entry_point=True,
                is_leaf_node=False
            )
        }

        # Mock NetworkX graph
        import networkx as nx
        self.mock_graph.graph = nx.DiGraph()
        self.mock_graph.graph.add_node("/src/test.vue")

    def teardown_method(self) -> None:
        """Clean up test fixtures."""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)

    def test_xml_formatter_initialization(self) -> None:
        """Test XMLFormatter initialization."""
        formatter = XMLFormatter(self.mock_graph)
        assert formatter.graph == self.mock_graph

    def test_xml_format(self) -> None:
        """Test XML formatting."""
        formatter = XMLFormatter(self.mock_graph)
        result = formatter.format()
        
        # Should be valid XML
        root = ET.fromstring(result)
        assert root.tag == "dependency_analysis"
        
        # Should contain expected elements
        assert root.find("project_info") is not None
        assert root.find("statistics") is not None
        assert root.find("files") is not None

    def test_xml_structure(self) -> None:
        """Test XML structure and content."""
        formatter = XMLFormatter(self.mock_graph)
        result = formatter.format()
        
        root = ET.fromstring(result)
        
        # Check project info
        project_info = root.find("project_info")
        assert project_info.find("root").text == str(self.temp_dir)
        
        # Check statistics
        stats = root.find("statistics")
        assert stats.find("total_files").text == "2"


class TestSARIFFormatter:
    """Test the SARIFFormatter class."""

    def setup_method(self) -> None:
        """Set up test fixtures."""
        self.temp_dir = Path(tempfile.mkdtemp())
        self.mock_graph = Mock(spec=DependencyGraph)
        self.mock_graph.project_root = self.temp_dir
        
        # Mock circular dependencies for SARIF issues
        self.mock_graph.find_circular_dependencies.return_value = [
            ["/src/A.vue", "/src/B.vue", "/src/A.vue"]
        ]
        
        self.mock_graph.nodes = {}

    def teardown_method(self) -> None:
        """Clean up test fixtures."""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)

    def test_sarif_formatter_initialization(self) -> None:
        """Test SARIFFormatter initialization."""
        formatter = SARIFFormatter(self.mock_graph)
        assert formatter.graph == self.mock_graph

    def test_sarif_format(self) -> None:
        """Test SARIF formatting."""
        formatter = SARIFFormatter(self.mock_graph)
        result = formatter.format()
        
        # Should be valid JSON
        data = json.loads(result)
        
        # Should follow SARIF schema
        assert data["$schema"] is not None
        assert data["version"] == "2.1.0"
        assert "runs" in data
        assert len(data["runs"]) == 1

    def test_sarif_structure(self) -> None:
        """Test SARIF structure and content."""
        formatter = SARIFFormatter(self.mock_graph)
        result = formatter.format()
        
        data = json.loads(result)
        run = data["runs"][0]
        
        # Check tool information
        assert "tool" in run
        assert run["tool"]["driver"]["name"] == "Vue.js and React Dependency Analyzer"
        
        # Check results
        assert "results" in run


class TestGitHubActionsFormatter:
    """Test the GitHubActionsFormatter class."""

    def setup_method(self) -> None:
        """Set up test fixtures."""
        self.temp_dir = Path(tempfile.mkdtemp())
        self.mock_graph = Mock(spec=DependencyGraph)
        self.mock_graph.project_root = self.temp_dir
        
        self.mock_graph.get_statistics.return_value = {
            "total_files": 5,
            "total_dependencies": 10,
            "entry_points": 2,
            "leaf_nodes": 3,
            "circular_dependencies": 1
        }

        # Mock nodes for iteration
        self.mock_graph.nodes = {}

    def teardown_method(self) -> None:
        """Clean up test fixtures."""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)

    def test_github_actions_formatter_initialization(self) -> None:
        """Test GitHubActionsFormatter initialization."""
        formatter = GitHubActionsFormatter(self.mock_graph)
        assert formatter.graph == self.mock_graph

    def test_github_actions_format(self) -> None:
        """Test GitHub Actions formatting."""
        formatter = GitHubActionsFormatter(self.mock_graph)
        result = formatter.format()
        
        # Should contain GitHub Actions specific formatting
        assert isinstance(result, str)
        assert "# Dependency Analysis Summary" in result
        
        # Should contain statistics
        assert "**Total Files:** 5" in result
        assert "**Total Dependencies:** 10" in result

    def test_github_actions_with_issues(self) -> None:
        """Test GitHub Actions formatting with issues."""
        # Mock circular dependencies
        self.mock_graph.find_circular_dependencies.return_value = [
            ["/src/A.vue", "/src/B.vue", "/src/A.vue"]
        ]
        
        formatter = GitHubActionsFormatter(self.mock_graph)
        result = formatter.format()
        
        # Should include warnings about issues
        assert "⚠️" in result or "warning" in result.lower()


class TestFormatterEdgeCases:
    """Test formatter edge cases and error handling."""

    def test_formatters_with_empty_graph(self) -> None:
        """Test formatters with empty dependency graph."""
        mock_graph = Mock(spec=DependencyGraph)
        mock_graph.project_root = Path("/empty")
        mock_graph.nodes = {}
        mock_graph.project_info = {
            "root": "/empty",
            "has_package_json": False,
            "has_src_dir": False
        }
        mock_graph.get_statistics.return_value = {
            "total_files": 0,
            "total_dependencies": 0,
            "entry_points": 0,
            "leaf_nodes": 0
        }
        mock_graph.find_circular_dependencies.return_value = []

        # Mock NetworkX graph
        import networkx as nx
        mock_graph.graph = nx.DiGraph()

        # Test all formatters handle empty graph
        csv_formatter = CSVFormatter(mock_graph)
        csv_result = csv_formatter.format("files")
        assert isinstance(csv_result, str)
        
        xml_formatter = XMLFormatter(mock_graph)
        xml_result = xml_formatter.format()
        root = ET.fromstring(xml_result)
        assert root.find("statistics/total_files").text == "0"
        
        sarif_formatter = SARIFFormatter(mock_graph)
        sarif_result = sarif_formatter.format()
        sarif_data = json.loads(sarif_result)
        assert len(sarif_data["runs"][0]["results"]) == 0
        
        gh_formatter = GitHubActionsFormatter(mock_graph)
        gh_result = gh_formatter.format()
        assert "**Total Files:** 0" in gh_result

    def test_formatters_with_large_data(self) -> None:
        """Test formatters with large amounts of data."""
        mock_graph = Mock(spec=DependencyGraph)
        mock_graph.project_root = Path("/large")
        mock_graph.get_statistics.return_value = {
            "total_files": 1000,
            "total_dependencies": 5000
        }

        # Create many mock nodes
        nodes = {}
        for i in range(100):  # Reduced for test performance
            nodes[f"/src/file{i}.vue"] = DependencyNode(
                file_path=f"/src/file{i}.vue",
                imports=[],
                exports=[f"Component{i}"],
                is_entry_point=(i == 0),
                is_leaf_node=(i > 90)
            )

        mock_graph.nodes = nodes

        # Mock NetworkX graph
        import networkx as nx
        mock_graph.graph = nx.DiGraph()
        for file_path in nodes.keys():
            mock_graph.graph.add_node(file_path)
        
        # Test CSV formatter handles large data
        csv_formatter = CSVFormatter(mock_graph)
        csv_result = csv_formatter.format("files")
        
        # Should contain all files
        lines = csv_result.split('\n')
        assert len(lines) > 100  # Header + data rows
