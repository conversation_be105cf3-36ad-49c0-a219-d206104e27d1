"""
Unit tests for the validation module.

Tests validation functions for input parameters, file paths, project structure,
and configuration options.
"""

import os
import tempfile
from pathlib import Path
from unittest.mock import patch

import pytest

from src.diff_analyzer.exceptions import ConfigurationError, ProjectStructureError, ValidationError
from src.diff_analyzer.validation import (
    validate_configuration,
    validate_exclude_patterns,
    validate_file_path,
    validate_max_workers,
    validate_output_format,
    validate_project_path,
    validate_project_structure,
    validate_supported_extensions,
)


class TestValidateProjectPath:
    """Test project path validation."""

    def setup_method(self) -> None:
        """Set up test fixtures."""
        self.temp_dir = Path(tempfile.mkdtemp())

    def teardown_method(self) -> None:
        """Clean up test fixtures."""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)

    def test_validate_existing_directory(self) -> None:
        """Test validation of existing directory."""
        result = validate_project_path(str(self.temp_dir))
        assert result == self.temp_dir.resolve()

    def test_validate_empty_path(self) -> None:
        """Test validation of empty path."""
        with pytest.raises(ValidationError, match="Project path cannot be empty"):
            validate_project_path("")

    def test_validate_nonexistent_path(self) -> None:
        """Test validation of non-existent path."""
        with pytest.raises(ValidationError, match="Project path does not exist"):
            validate_project_path("/nonexistent/path")

    def test_validate_file_instead_of_directory(self) -> None:
        """Test validation when path points to file instead of directory."""
        test_file = self.temp_dir / "test.txt"
        test_file.write_text("test")

        with pytest.raises(ValidationError, match="Project path is not a directory"):
            validate_project_path(str(test_file))

    @patch("os.access")
    def test_validate_unreadable_directory(self, mock_access) -> None:
        """Test validation of unreadable directory."""
        mock_access.return_value = False

        with pytest.raises(ValidationError, match="Project path is not readable"):
            validate_project_path(str(self.temp_dir))


class TestValidateExcludePatterns:
    """Test exclude patterns validation."""

    def test_validate_valid_patterns(self) -> None:
        """Test validation of valid exclude patterns."""
        patterns = ["node_modules", "*.pyc", "dist"]
        result = validate_exclude_patterns(patterns)
        assert result == patterns

    def test_validate_empty_patterns(self) -> None:
        """Test validation of empty patterns list."""
        result = validate_exclude_patterns([])
        assert result == []

    def test_validate_non_list_patterns(self) -> None:
        """Test validation of non-list patterns."""
        with pytest.raises(ValidationError, match="Exclude patterns must be a list"):
            validate_exclude_patterns("not_a_list")

    def test_validate_non_string_pattern(self) -> None:
        """Test validation of non-string pattern in list."""
        with pytest.raises(ValidationError, match="Exclude pattern must be a string: 123"):
            validate_exclude_patterns(["valid", 123, "also_valid"])

    def test_validate_empty_string_pattern(self) -> None:
        """Test validation of empty string pattern (should be skipped)."""
        result = validate_exclude_patterns(["valid", "", "also_valid"])
        # Empty patterns should be skipped
        assert result == ["valid", "also_valid"]


class TestValidateOutputFormat:
    """Test output format validation."""

    def test_validate_valid_formats(self) -> None:
        """Test validation of valid output formats."""
        valid_formats = ["json", "dot", "mermaid", "text"]
        for fmt in valid_formats:
            result = validate_output_format(fmt)
            assert result == fmt

    def test_validate_case_insensitive(self) -> None:
        """Test that format validation is case insensitive."""
        result = validate_output_format("JSON")
        assert result == "json"

        result = validate_output_format("  MERMAID  ")
        assert result == "mermaid"

    def test_validate_invalid_format(self) -> None:
        """Test validation of invalid output format."""
        with pytest.raises(ValidationError, match="Invalid output format"):
            validate_output_format("invalid_format")

    def test_validate_non_string_format(self) -> None:
        """Test validation of non-string format."""
        with pytest.raises(ValidationError, match="Output format must be a string"):
            validate_output_format(123)


class TestValidateFilePath:
    """Test file path validation."""

    def setup_method(self) -> None:
        """Set up test fixtures."""
        self.temp_dir = Path(tempfile.mkdtemp())
        self.test_file = self.temp_dir / "test.vue"
        self.test_file.write_text("<template></template>")

    def teardown_method(self) -> None:
        """Clean up test fixtures."""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)

    def test_validate_existing_file(self) -> None:
        """Test validation of existing file."""
        result = validate_file_path(str(self.test_file))
        assert str(result) == str(self.test_file.resolve())

    def test_validate_nonexistent_file(self) -> None:
        """Test validation of non-existent file."""
        with pytest.raises(ValidationError, match="File does not exist"):
            validate_file_path("/nonexistent/file.vue")

    def test_validate_directory_as_file(self) -> None:
        """Test validation when path points to directory."""
        with pytest.raises(ValidationError, match="Path is not a file"):
            validate_file_path(str(self.temp_dir))

    def test_validate_empty_file_path(self) -> None:
        """Test validation of empty file path."""
        with pytest.raises(ValidationError, match="File path cannot be empty"):
            validate_file_path("")


class TestValidateSupportedExtensions:
    """Test supported extensions validation."""

    def test_validate_valid_extensions(self) -> None:
        """Test validation of valid extensions."""
        extensions = {".vue", ".jsx", ".tsx", ".ts", ".js"}
        result = validate_supported_extensions(extensions)
        assert result == extensions

    def test_validate_extensions_as_list(self) -> None:
        """Test validation of extensions provided as list."""
        extensions = [".vue", ".jsx"]
        result = validate_supported_extensions(extensions)
        assert result == {".vue", ".jsx"}

    def test_validate_non_iterable_extensions(self) -> None:
        """Test validation of non-iterable extensions."""
        with pytest.raises(ValidationError, match="Extensions must be a set or list"):
            validate_supported_extensions(123)  # type: ignore

    def test_validate_non_string_extension(self) -> None:
        """Test validation of non-string extension."""
        with pytest.raises(ValidationError, match="Extension must be a string: 123"):
            validate_supported_extensions([".vue", 123])  # type: ignore

    def test_validate_extension_without_dot(self) -> None:
        """Test validation of extension without leading dot."""
        with pytest.raises(ValidationError, match="Extension must start with '.': vue"):
            validate_supported_extensions(["vue", ".jsx"])

    def test_validate_empty_extensions(self) -> None:
        """Test validation of empty extensions (should return empty set)."""
        result = validate_supported_extensions([])
        assert result == set()


class TestValidateMaxWorkers:
    """Test max workers validation."""

    def test_validate_valid_worker_count(self) -> None:
        """Test validation of valid worker count."""
        result = validate_max_workers(4)
        assert result == 4

    def test_validate_none_workers(self) -> None:
        """Test validation of None (auto-detect)."""
        result = validate_max_workers(None)
        # Should return computed value based on CPU count
        assert isinstance(result, int)
        assert result > 0

    def test_validate_zero_workers(self) -> None:
        """Test validation of zero workers."""
        with pytest.raises(ValidationError, match="max_workers must be at least 1"):
            validate_max_workers(0)

    def test_validate_negative_workers(self) -> None:
        """Test validation of negative workers."""
        with pytest.raises(ValidationError, match="max_workers must be at least 1"):
            validate_max_workers(-1)

    def test_validate_non_integer_workers(self) -> None:
        """Test validation of non-integer workers."""
        with pytest.raises(ValidationError, match="max_workers must be an integer"):
            validate_max_workers("4")  # type: ignore


class TestValidateProjectStructure:
    """Test project structure validation."""

    def setup_method(self) -> None:
        """Set up test fixtures."""
        self.temp_dir = Path(tempfile.mkdtemp())

    def teardown_method(self) -> None:
        """Clean up test fixtures."""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)

    def test_validate_valid_project_structure(self) -> None:
        """Test validation of valid project structure."""
        # Create some files
        (self.temp_dir / "src").mkdir()
        (self.temp_dir / "src" / "App.vue").write_text("<template></template>")
        (self.temp_dir / "package.json").write_text('{"name": "test"}')

        result = validate_project_structure(self.temp_dir)
        assert isinstance(result, dict)
        assert "has_package_json" in result
        assert "has_src_dir" in result

    def test_validate_empty_project(self) -> None:
        """Test validation of empty project."""
        result = validate_project_structure(self.temp_dir)
        assert result["has_package_json"] is False
        assert result["has_src_dir"] is False


class TestValidateConfiguration:
    """Test configuration validation."""

    def test_validate_valid_configuration(self) -> None:
        """Test validation of valid configuration."""
        config = {
            "max_workers": 4,
            "exclude_patterns": ["node_modules"],
            "output_format": "json",
            "supported_extensions": [".vue", ".js"]
        }
        result = validate_configuration(config)
        assert isinstance(result, dict)
        assert result["max_workers"] == 4

    def test_validate_non_dict_configuration(self) -> None:
        """Test validation of non-dict configuration."""
        with pytest.raises(ConfigurationError, match="Configuration must be a dictionary"):
            validate_configuration("not_a_dict")

    def test_validate_configuration_with_invalid_values(self) -> None:
        """Test validation of configuration with invalid values."""
        config = {"max_workers": -1}
        with pytest.raises(ValidationError):
            validate_configuration(config)

    def test_validate_partial_configuration(self) -> None:
        """Test validation of partial configuration."""
        config = {"max_workers": 2}
        result = validate_configuration(config)
        assert result["max_workers"] == 2
        # Should only contain validated keys
        assert len(result) == 1
