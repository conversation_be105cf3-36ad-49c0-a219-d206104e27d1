"""
Unit tests for the advanced formatters module.

Tests the MetricsJSONFormatter and MarkdownReportFormatter classes
and their ability to generate enhanced output formats with metrics and analysis.
"""

import json
import tempfile
from pathlib import Path
from unittest.mock import Mock

import pytest

from src.diff_analyzer.advanced_formatters import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>er, MetricsJSONFormatter
from src.diff_analyzer.dependency_analyzer import <PERSON>pendencyNode, DependencyType, ImportInfo, ImportType
from src.diff_analyzer.dependency_graph import DependencyGraph


class TestMetricsJSONFormatter:
    """Test the MetricsJSONFormatter class."""

    def setup_method(self) -> None:
        """Set up test fixtures."""
        self.temp_dir = Path(tempfile.mkdtemp())
        
        # Create a mock dependency graph
        self.mock_graph = Mock(spec=DependencyGraph)
        self.mock_graph.project_root = self.temp_dir
        
        # Mock configuration
        mock_config = Mock()
        mock_config.get_config_sources.return_value = ["defaults", "project_config"]
        self.mock_graph.config = mock_config
        self.mock_graph.max_workers = 4
        self.mock_graph.supported_extensions = {".vue", ".ts", ".js"}
        self.mock_graph.project_info = {"has_package_json": True, "has_src_dir": True}
        
        # Create sample nodes
        self.nodes = {
            "/src/main.ts": DependencyNode(
                file_path="/src/main.ts",
                imports=[
                    ImportInfo(
                        source_file="/src/main.ts",
                        imported_module="/src/App.vue",
                        import_type=ImportType.DEFAULT,
                        dependency_type=DependencyType.LOCAL,
                        imported_names=["App"],
                        line_number=1
                    )
                ],
                exports=["main"],
                is_entry_point=True,
                is_leaf_node=False
            ),
            "/src/App.vue": DependencyNode(
                file_path="/src/App.vue",
                imports=[],
                exports=["App"],
                is_entry_point=False,
                is_leaf_node=True
            )
        }
        
        self.mock_graph.nodes = self.nodes
        
        # Mock NetworkX graph
        import networkx as nx
        self.nx_graph = nx.DiGraph()
        self.nx_graph.add_edge("/src/main.ts", "/src/App.vue")
        self.mock_graph.graph = self.nx_graph
        
        # Mock statistics
        self.mock_graph.get_statistics.return_value = {
            "total_files": 2,
            "total_dependencies": 1,
            "entry_points": 1,
            "leaf_nodes": 1
        }
        
        # Mock advanced metrics
        self.mock_graph.get_advanced_metrics.return_value = {
            "basic_metrics": {"total_files": 2},
            "complexity_metrics": {"cyclomatic_complexity": 1},
            "architectural_metrics": {"layered_architecture": True}
        }
        
        # Mock error methods
        self.mock_graph.has_errors.return_value = False
        self.mock_graph.get_errors.return_value = []
        self.mock_graph.get_error_summary.return_value = {}

    def teardown_method(self) -> None:
        """Clean up test fixtures."""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)

    def test_formatter_initialization(self) -> None:
        """Test MetricsJSONFormatter initialization."""
        formatter = MetricsJSONFormatter(self.mock_graph)
        assert formatter.graph == self.mock_graph

    def test_basic_format(self) -> None:
        """Test basic formatting without metrics."""
        formatter = MetricsJSONFormatter(self.mock_graph)
        result = formatter.format(include_metrics=False)
        
        # Should be valid JSON
        data = json.loads(result)
        
        assert "project_root" in data
        assert "analysis_timestamp" in data
        assert "configuration" in data
        assert "basic_statistics" in data
        assert "files" in data
        assert "dependencies" in data
        
        # Should not include advanced metrics
        assert "advanced_metrics" not in data
        assert "recommendations" not in data

    def test_format_with_metrics(self) -> None:
        """Test formatting with advanced metrics."""
        formatter = MetricsJSONFormatter(self.mock_graph)
        result = formatter.format(include_metrics=True)
        
        data = json.loads(result)
        
        assert "advanced_metrics" in data
        assert "recommendations" in data
        
        # Check that advanced metrics are included
        assert data["advanced_metrics"]["basic_metrics"]["total_files"] == 2

    def test_format_with_file_analysis(self) -> None:
        """Test formatting with detailed file analysis."""
        # Mock the get_advanced_metrics method to return serializable data
        self.mock_graph.get_advanced_metrics.return_value = {
            "basic_metrics": {"total_files": 2, "total_dependencies": 1},
            "complexity_metrics": {"cyclomatic_complexity": 1}
        }

        # Mock the nodes to be empty to avoid serialization issues
        self.mock_graph.nodes = {}

        formatter = MetricsJSONFormatter(self.mock_graph)
        result = formatter.format(include_file_analysis=True)

        data = json.loads(result)

        assert "file_analysis" in data

    def test_format_with_errors(self) -> None:
        """Test formatting when graph has errors."""
        # Mock graph with errors
        self.mock_graph.has_errors.return_value = True
        self.mock_graph.get_errors.return_value = ["Error 1", "Error 2"]
        self.mock_graph.get_error_summary.return_value = {"ParseError": 2}
        
        formatter = MetricsJSONFormatter(self.mock_graph)
        result = formatter.format()
        
        data = json.loads(result)
        
        assert "errors" in data
        assert data["errors"]["error_count"] == 2
        assert "error_summary" in data["errors"]

    def test_get_timestamp(self) -> None:
        """Test timestamp generation."""
        formatter = MetricsJSONFormatter(self.mock_graph)
        timestamp = formatter._get_timestamp()
        
        # Should be ISO format timestamp
        assert isinstance(timestamp, str)
        assert "T" in timestamp  # ISO format contains T

    def test_get_config_info(self) -> None:
        """Test configuration information extraction."""
        formatter = MetricsJSONFormatter(self.mock_graph)
        config_info = formatter._get_config_info()
        
        assert "config_sources" in config_info
        assert "max_workers" in config_info
        assert "supported_extensions" in config_info
        assert "project_info" in config_info
        
        assert config_info["max_workers"] == 4
        assert ".vue" in config_info["supported_extensions"]

    def test_get_files_info(self) -> None:
        """Test file information extraction."""
        formatter = MetricsJSONFormatter(self.mock_graph)
        files_info = formatter._get_files_info()
        
        assert isinstance(files_info, dict)
        # Should have relative paths as keys
        assert any("main.ts" in key for key in files_info.keys())
        assert any("App.vue" in key for key in files_info.keys())

    def test_get_dependencies_info(self) -> None:
        """Test dependency information extraction."""
        formatter = MetricsJSONFormatter(self.mock_graph)
        deps_info = formatter._get_dependencies_info()
        
        assert isinstance(deps_info, dict)

    def test_format_import_info(self) -> None:
        """Test import information formatting."""
        formatter = MetricsJSONFormatter(self.mock_graph)
        import_info = ImportInfo(
            source_file="/src/test.vue",
            imported_module="vue",
            import_type=ImportType.NAMED,
            dependency_type=DependencyType.EXTERNAL,
            imported_names=["ref"],
            line_number=5
        )
        
        result = formatter._format_import_info(import_info)
        
        assert result["module"] == "vue"
        assert result["type"] == "NAMED"
        assert result["dependency_type"] == "EXTERNAL"
        assert result["imported_names"] == ["ref"]
        assert result["line_number"] == 5

    def test_generate_recommendations(self) -> None:
        """Test recommendations generation."""
        formatter = MetricsJSONFormatter(self.mock_graph)
        recommendations = formatter._generate_recommendations()
        
        assert isinstance(recommendations, dict)
        assert "high_priority" in recommendations
        assert "medium_priority" in recommendations
        assert "low_priority" in recommendations
        assert "optimizations" in recommendations


class TestMarkdownReportFormatter:
    """Test the MarkdownReportFormatter class."""

    def setup_method(self) -> None:
        """Set up test fixtures."""
        self.temp_dir = Path(tempfile.mkdtemp())
        
        # Create a mock dependency graph (reuse from MetricsJSONFormatter tests)
        self.mock_graph = Mock(spec=DependencyGraph)
        self.mock_graph.project_root = self.temp_dir
        
        # Mock configuration
        mock_config = Mock()
        mock_config.get_config_sources.return_value = ["defaults"]
        self.mock_graph.config = mock_config
        self.mock_graph.max_workers = 4
        self.mock_graph.supported_extensions = {".vue", ".ts"}
        self.mock_graph.project_info = {"has_package_json": True, "has_src_dir": True}

        # Mock statistics
        self.mock_graph.get_statistics.return_value = {
            "total_files": 5,
            "total_dependencies": 8,
            "entry_points": 2,
            "leaf_nodes": 3,
            "circular_dependencies": 0
        }
        
        # Mock advanced metrics
        self.mock_graph.get_advanced_metrics.return_value = {
            "complexity_metrics": {
                "cyclomatic_complexity": 5,
                "max_dependency_depth": 4
            },
            "quality_metrics": {
                "code_smells": ["Large file: /src/large.vue"]
            }
        }
        
        self.mock_graph.has_errors.return_value = False

        # Mock nodes and graph
        self.mock_graph.nodes = {}

        # Mock NetworkX graph
        import networkx as nx
        self.mock_graph.graph = nx.DiGraph()

    def teardown_method(self) -> None:
        """Clean up test fixtures."""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)

    def test_markdown_formatter_initialization(self) -> None:
        """Test MarkdownReportFormatter initialization."""
        formatter = MarkdownReportFormatter(self.mock_graph)
        assert formatter.graph == self.mock_graph

    def test_basic_markdown_format(self) -> None:
        """Test basic markdown formatting."""
        formatter = MarkdownReportFormatter(self.mock_graph)
        result = formatter.format()
        
        # Should be valid markdown
        assert isinstance(result, str)
        assert "# Dependency Analysis Report" in result
        assert "## Executive Summary" in result
        assert "## Project Statistics" in result

    def test_markdown_with_metrics(self) -> None:
        """Test markdown formatting with metrics."""
        formatter = MarkdownReportFormatter(self.mock_graph)
        result = formatter.format(include_metrics=True)
        
        assert "## Detailed Metrics" in result
        assert "### Complexity Analysis" in result

    def test_markdown_with_recommendations(self) -> None:
        """Test markdown formatting with recommendations."""
        formatter = MarkdownReportFormatter(self.mock_graph)
        result = formatter.format(include_recommendations=True)
        
        assert "## Recommendations" in result

    def test_markdown_sections(self) -> None:
        """Test that all expected markdown sections are present."""
        formatter = MarkdownReportFormatter(self.mock_graph)
        result = formatter.format(include_metrics=True, include_recommendations=True)
        
        expected_sections = [
            "# Dependency Analysis Report",
            "## Executive Summary",
            "## Project Statistics",
            "## Detailed Metrics",
            "### Complexity Analysis",
            "## Recommendations",
            "## Appendix"
        ]
        
        for section in expected_sections:
            assert section in result

    def test_statistics_formatting(self) -> None:
        """Test statistics section formatting."""
        formatter = MarkdownReportFormatter(self.mock_graph)
        stats_section = formatter._generate_basic_statistics()

        assert "| Total Files | 5 |" in stats_section
        assert "| Total Dependencies | 8 |" in stats_section
        assert "| Entry Points | 2 |" in stats_section

    def test_complexity_formatting(self) -> None:
        """Test complexity section formatting."""
        formatter = MarkdownReportFormatter(self.mock_graph)
        metrics_section = formatter._generate_metrics_analysis()

        assert "Metrics" in metrics_section
        assert "Analysis" in metrics_section

    def test_quality_formatting(self) -> None:
        """Test quality section formatting."""
        formatter = MarkdownReportFormatter(self.mock_graph)
        recommendations_section = formatter._generate_recommendations_section()

        assert "Recommendations" in recommendations_section

    def test_appendix_formatting(self) -> None:
        """Test appendix section formatting."""
        formatter = MarkdownReportFormatter(self.mock_graph)
        appendix_section = formatter._generate_appendix()

        assert "## Appendix" in appendix_section
        assert "Configuration" in appendix_section


class TestFormatterEdgeCases:
    """Test formatter edge cases and error handling."""

    def test_formatter_with_empty_graph(self) -> None:
        """Test formatters with empty dependency graph."""
        mock_graph = Mock(spec=DependencyGraph)
        mock_graph.project_root = Path("/empty")
        mock_graph.nodes = {}
        mock_graph.get_statistics.return_value = {
            "total_files": 0,
            "total_dependencies": 0,
            "entry_points": 0,
            "leaf_nodes": 0
        }

        # Mock get_advanced_metrics to return proper data
        mock_graph.get_advanced_metrics.return_value = {
            "complexity_metrics": {"cyclomatic_complexity": 0},
            "quality_metrics": {"code_smells": []}
        }
        mock_graph.has_errors.return_value = False

        # Mock configuration
        mock_config = Mock()
        mock_config.get_config_sources.return_value = ["defaults"]
        mock_graph.config = mock_config
        mock_graph.max_workers = 4
        mock_graph.supported_extensions = {".vue", ".ts", ".js"}
        mock_graph.project_info = {"has_package_json": False, "has_src_dir": False}
        mock_graph.nodes = {}

        # Mock NetworkX graph
        import networkx as nx
        mock_graph.graph = nx.DiGraph()

        # Test JSON formatter
        json_formatter = MetricsJSONFormatter(mock_graph)
        json_result = json_formatter.format()
        data = json.loads(json_result)
        assert data["basic_statistics"]["total_files"] == 0
        
        # Test Markdown formatter
        md_formatter = MarkdownReportFormatter(mock_graph)
        md_result = md_formatter.format()
        assert "| Total Files | 0 |" in md_result

    def test_formatter_with_large_numbers(self) -> None:
        """Test formatters with large numbers."""
        mock_graph = Mock(spec=DependencyGraph)
        mock_graph.project_root = Path("/large")
        mock_graph.get_statistics.return_value = {
            "total_files": 10000,
            "total_dependencies": 50000,
            "entry_points": 100,
            "leaf_nodes": 2000
        }
        mock_graph.has_errors.return_value = False

        # Mock get_advanced_metrics to return proper data
        mock_graph.get_advanced_metrics.return_value = {
            "complexity_metrics": {"cyclomatic_complexity": 100},
            "quality_metrics": {"code_smells": []}
        }

        # Mock configuration
        mock_config = Mock()
        mock_config.get_config_sources.return_value = ["defaults"]
        mock_graph.config = mock_config
        mock_graph.max_workers = 4
        mock_graph.supported_extensions = {".vue", ".ts", ".js"}
        mock_graph.project_info = {"has_package_json": True, "has_src_dir": True}
        mock_graph.nodes = {}

        # Mock NetworkX graph
        import networkx as nx
        mock_graph.graph = nx.DiGraph()

        json_formatter = MetricsJSONFormatter(mock_graph)
        result = json_formatter.format()
        data = json.loads(result)
        
        assert data["basic_statistics"]["total_files"] == 10000
        assert data["basic_statistics"]["total_dependencies"] == 50000


class TestFormatterIntegration:
    """Test formatter integration with real dependency graph."""

    def setup_method(self) -> None:
        """Set up test fixtures with real graph."""
        self.temp_dir = Path(tempfile.mkdtemp())

        # Create actual files for testing
        src_dir = self.temp_dir / "src"
        src_dir.mkdir()

        (src_dir / "main.ts").write_text("""
import App from './App.vue'
import { createApp } from 'vue'

createApp(App).mount('#app')
""")

        (src_dir / "App.vue").write_text("""
<template>
  <div>Hello World</div>
</template>

<script lang="ts">
import { defineComponent } from 'vue'

export default defineComponent({
  name: 'App'
})
</script>
""")

    def teardown_method(self) -> None:
        """Clean up test fixtures."""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)

    def test_formatters_with_real_graph(self) -> None:
        """Test formatters with real dependency graph."""
        # This would require a real DependencyGraph instance
        # For now, we'll use mocks but this shows the integration pattern
        mock_graph = Mock(spec=DependencyGraph)
        mock_graph.project_root = self.temp_dir
        mock_graph.get_statistics.return_value = {
            "total_files": 2,
            "total_dependencies": 1,
            "entry_points": 1,
            "leaf_nodes": 1
        }
        mock_graph.has_errors.return_value = False

        # Mock get_advanced_metrics to return proper data
        mock_graph.get_advanced_metrics.return_value = {
            "complexity_metrics": {"cyclomatic_complexity": 2},
            "quality_metrics": {"code_smells": []}
        }

        # Mock configuration
        mock_config = Mock()
        mock_config.get_config_sources.return_value = ["defaults"]
        mock_graph.config = mock_config
        mock_graph.max_workers = 4
        mock_graph.supported_extensions = {".vue", ".ts", ".js"}
        mock_graph.project_info = {"has_package_json": True, "has_src_dir": True}
        mock_graph.nodes = {}

        # Mock NetworkX graph
        import networkx as nx
        mock_graph.graph = nx.DiGraph()

        # Test both formatters work together
        json_formatter = MetricsJSONFormatter(mock_graph)
        md_formatter = MarkdownReportFormatter(mock_graph)

        json_result = json_formatter.format()
        md_result = md_formatter.format()

        assert isinstance(json_result, str)
        assert isinstance(md_result, str)

        # Both should contain project information
        json_data = json.loads(json_result)
        assert "project_root" in json_data
        assert "# Dependency Analysis Report" in md_result
