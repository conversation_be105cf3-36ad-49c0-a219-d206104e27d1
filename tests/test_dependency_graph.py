"""
Unit tests for the dependency graph module.

Tests the DependencyGraph class and its ability to analyze projects,
detect circular dependencies, generate statistics, and perform graph algorithms.
"""

import tempfile
from pathlib import Path
from unittest.mock import patch

import networkx as nx

from src.diff_analyzer.dependency_analyzer import (
    DependencyNode,
    DependencyType,
    ImportInfo,
    ImportType,
)
from src.diff_analyzer.dependency_graph import DependencyGraph


class TestDependencyGraph:
    """Test the DependencyGraph class."""

    def setup_method(self):
        """Set up test fixtures."""
        self.temp_dir = tempfile.mkdtemp()
        self.graph = DependencyGraph(self.temp_dir)

    def teardown_method(self):
        """Clean up test fixtures."""
        import shutil

        shutil.rmtree(self.temp_dir, ignore_errors=True)

    def test_dependency_graph_initialization(self):
        """Test DependencyGraph initialization."""
        assert self.graph.project_root == Path(self.temp_dir)
        assert isinstance(self.graph.graph, nx.DiGraph)
        assert len(self.graph.nodes) == 0
        assert self.graph.vue_parser is not None
        assert self.graph.react_parser is not None

    def test_find_files_to_analyze(self):
        """Test finding files to analyze in project."""
        # Create test files
        (Path(self.temp_dir) / "src").mkdir()
        (Path(self.temp_dir) / "src" / "App.vue").write_text("<template></template>")
        (Path(self.temp_dir) / "src" / "main.ts").write_text("import App from './App.vue'")
        (Path(self.temp_dir) / "src" / "Component.tsx").write_text("import React from 'react'")
        (Path(self.temp_dir) / "README.md").write_text("# Project")

        # Create excluded directory
        (Path(self.temp_dir) / "node_modules").mkdir()
        (Path(self.temp_dir) / "node_modules" / "package.js").write_text("// package")

        files = self.graph._find_files_to_analyze(["node_modules"])

        # Should find Vue, TypeScript, and React files but not excluded ones
        file_names = [Path(f).name for f in files]
        assert "App.vue" in file_names
        assert "main.ts" in file_names
        assert "Component.tsx" in file_names
        assert "package.js" not in file_names
        assert "README.md" not in file_names

    def test_find_files_to_analyze_empty_directory(self):
        """Test finding files in empty directory."""
        files = self.graph._find_files_to_analyze(["node_modules", ".git"])
        assert len(files) == 0

    def test_parse_file_vue(self):
        """Test parsing Vue file."""
        vue_file = Path(self.temp_dir) / "Component.vue"
        vue_file.write_text(
            """
<template>
  <div>Hello</div>
</template>

<script>
export default {
  name: 'Component'
}
</script>
"""
        )

        with patch.object(self.graph.vue_parser, "parse_file") as mock_parse:
            mock_node = DependencyNode(
                file_path=str(vue_file),
                imports=[],
                exports=["Component"],
                is_entry_point=False,
                is_leaf_node=True,
            )
            mock_parse.return_value = mock_node

            result = self.graph._parse_file(str(vue_file))

            assert result == mock_node
            mock_parse.assert_called_once_with(str(vue_file))

    def test_parse_file_react(self):
        """Test parsing React file."""
        react_file = Path(self.temp_dir) / "Component.tsx"
        react_file.write_text(
            """
import React from 'react';

export const Component = () => {
  return <div>Hello</div>;
};
"""
        )

        with patch.object(self.graph.react_parser, "parse_file") as mock_parse:
            mock_node = DependencyNode(
                file_path=str(react_file),
                imports=[],
                exports=["Component"],
                is_entry_point=False,
                is_leaf_node=True,
            )
            mock_parse.return_value = mock_node

            result = self.graph._parse_file(str(react_file))

            assert result == mock_node
            mock_parse.assert_called_once_with(str(react_file))

    def test_parse_file_unsupported(self):
        """Test parsing unsupported file type."""
        unsupported_file = Path(self.temp_dir) / "file.txt"
        unsupported_file.write_text("Some text")

        result = self.graph._parse_file(str(unsupported_file))

        assert result is None

    def test_node_to_attributes(self):
        """Test converting node to graph attributes."""
        node = DependencyNode(
            file_path="/test/Component.vue",
            imports=[
                ImportInfo(
                    source_file="/test/Component.vue",
                    imported_module="vue",
                    import_type=ImportType.NAMED,
                    dependency_type=DependencyType.EXTERNAL,
                    imported_names=["defineComponent"],
                    line_number=1,
                )
            ],
            exports=["Component"],
            is_entry_point=True,
            is_leaf_node=False,
        )

        attrs = self.graph._node_to_attributes(node)

        assert attrs["is_entry_point"] is True
        assert attrs["is_leaf_node"] is False
        assert attrs["exports"] == ["Component"]
        assert attrs["import_count"] == 1

    def test_build_graph_edges(self):
        """Test building graph edges from imports."""
        # Create mock nodes
        main_node = DependencyNode(
            file_path="/test/main.ts",
            imports=[
                ImportInfo(
                    source_file="/test/main.ts",
                    imported_module="/test/App.vue",
                    import_type=ImportType.DEFAULT,
                    dependency_type=DependencyType.LOCAL,
                    imported_names=["App"],
                    line_number=1,
                )
            ],
            exports=["main"],
            is_entry_point=True,
            is_leaf_node=False,
        )

        app_node = DependencyNode(
            file_path="/test/App.vue",
            imports=[],
            exports=["App"],
            is_entry_point=False,
            is_leaf_node=True,
        )

        # Add nodes to graph
        self.graph.nodes[main_node.file_path] = main_node
        self.graph.nodes[app_node.file_path] = app_node
        self.graph.graph.add_node(main_node.file_path)
        self.graph.graph.add_node(app_node.file_path)

        # Build edges
        self.graph._build_graph_edges()

        # Check that edge was created
        assert self.graph.graph.has_edge(main_node.file_path, app_node.file_path)

    def test_analyze_graph_properties(self):
        """Test analyzing graph properties."""
        # Create a simple graph
        self.graph.graph.add_node("/test/main.ts")
        self.graph.graph.add_node("/test/App.vue")
        self.graph.graph.add_edge("/test/main.ts", "/test/App.vue")

        # Add corresponding nodes
        self.graph.nodes["/test/main.ts"] = DependencyNode(
            file_path="/test/main.ts",
            imports=[],
            exports=["main"],
            is_entry_point=True,
            is_leaf_node=False,
        )
        self.graph.nodes["/test/App.vue"] = DependencyNode(
            file_path="/test/App.vue",
            imports=[],
            exports=["App"],
            is_entry_point=False,
            is_leaf_node=True,
        )

        self.graph._analyze_graph_properties()

        # Check that properties were analyzed
        assert len(self.graph.nodes) == 2

    def test_get_dependencies(self):
        """Test getting dependencies of a file."""
        # Create graph with dependencies
        self.graph.graph.add_edge("/test/main.ts", "/test/App.vue")
        self.graph.graph.add_edge("/test/main.ts", "/test/router.ts")

        dependencies = self.graph.get_dependencies("/test/main.ts")

        assert "/test/App.vue" in dependencies
        assert "/test/router.ts" in dependencies
        assert len(dependencies) == 2

    def test_get_dependencies_nonexistent(self):
        """Test getting dependencies of nonexistent file."""
        dependencies = self.graph.get_dependencies("/test/nonexistent.ts")
        assert len(dependencies) == 0

    def test_get_dependents(self):
        """Test getting dependents of a file."""
        # Create graph with dependents
        self.graph.graph.add_edge("/test/main.ts", "/test/App.vue")
        self.graph.graph.add_edge("/test/router.ts", "/test/App.vue")

        dependents = self.graph.get_dependents("/test/App.vue")

        assert "/test/main.ts" in dependents
        assert "/test/router.ts" in dependents
        assert len(dependents) == 2

    def test_get_dependents_nonexistent(self):
        """Test getting dependents of nonexistent file."""
        dependents = self.graph.get_dependents("/test/nonexistent.ts")
        assert len(dependents) == 0

    def test_get_entry_points(self):
        """Test getting entry points."""
        # Add nodes with entry point flags
        self.graph.nodes["/test/main.ts"] = DependencyNode(
            file_path="/test/main.ts",
            imports=[],
            exports=["main"],
            is_entry_point=True,
            is_leaf_node=False,
        )
        self.graph.nodes["/test/App.vue"] = DependencyNode(
            file_path="/test/App.vue",
            imports=[],
            exports=["App"],
            is_entry_point=False,
            is_leaf_node=True,
        )

        entry_points = self.graph.get_entry_points()

        assert "/test/main.ts" in entry_points
        assert "/test/App.vue" not in entry_points
        assert len(entry_points) == 1

    def test_get_leaf_nodes(self):
        """Test getting leaf nodes."""
        # Add nodes with leaf node flags
        self.graph.nodes["/test/main.ts"] = DependencyNode(
            file_path="/test/main.ts",
            imports=[],
            exports=["main"],
            is_entry_point=True,
            is_leaf_node=False,
        )
        self.graph.nodes["/test/utils.ts"] = DependencyNode(
            file_path="/test/utils.ts",
            imports=[],
            exports=["utils"],
            is_entry_point=False,
            is_leaf_node=True,
        )

        leaf_nodes = self.graph.get_leaf_nodes()

        assert "/test/utils.ts" in leaf_nodes
        assert "/test/main.ts" not in leaf_nodes
        assert len(leaf_nodes) == 1

    def test_find_circular_dependencies_no_cycles(self):
        """Test finding circular dependencies when none exist."""
        # Create acyclic graph
        self.graph.graph.add_edge("/test/main.ts", "/test/App.vue")
        self.graph.graph.add_edge("/test/App.vue", "/test/Component.vue")

        cycles = self.graph.find_circular_dependencies()

        assert len(cycles) == 0

    def test_find_circular_dependencies_with_cycles(self):
        """Test finding circular dependencies when they exist."""
        # Create cyclic graph
        self.graph.graph.add_edge("/test/A.vue", "/test/B.vue")
        self.graph.graph.add_edge("/test/B.vue", "/test/C.vue")
        self.graph.graph.add_edge("/test/C.vue", "/test/A.vue")

        cycles = self.graph.find_circular_dependencies()

        assert len(cycles) > 0
        # Should find the cycle A -> B -> C -> A
        cycle_files = set()
        for cycle in cycles:
            cycle_files.update(cycle)
        assert "/test/A.vue" in cycle_files
        assert "/test/B.vue" in cycle_files
        assert "/test/C.vue" in cycle_files

    def test_find_circular_dependencies_self_loop(self):
        """Test finding self-referencing circular dependencies."""
        # Create self-loop
        self.graph.graph.add_edge("/test/Component.vue", "/test/Component.vue")

        cycles = self.graph.find_circular_dependencies()

        assert len(cycles) > 0

    def test_get_external_dependencies(self):
        """Test getting external dependencies."""
        # Add nodes with external imports
        self.graph.nodes["/test/main.ts"] = DependencyNode(
            file_path="/test/main.ts",
            imports=[
                ImportInfo(
                    source_file="/test/main.ts",
                    imported_module="vue",
                    import_type=ImportType.NAMED,
                    dependency_type=DependencyType.EXTERNAL,
                    imported_names=["createApp"],
                    line_number=1,
                ),
                ImportInfo(
                    source_file="/test/main.ts",
                    imported_module="react",
                    import_type=ImportType.DEFAULT,
                    dependency_type=DependencyType.EXTERNAL,
                    imported_names=["React"],
                    line_number=2,
                ),
                ImportInfo(
                    source_file="/test/main.ts",
                    imported_module="./App.vue",
                    import_type=ImportType.DEFAULT,
                    dependency_type=DependencyType.LOCAL,
                    imported_names=["App"],
                    line_number=3,
                ),
            ],
            exports=["main"],
            is_entry_point=True,
            is_leaf_node=False,
        )

        external_deps = self.graph.get_external_dependencies()

        assert "vue" in external_deps
        assert "react" in external_deps
        assert "./App.vue" not in external_deps
        assert len(external_deps) == 2

    def test_get_statistics(self):
        """Test getting project statistics."""
        # Create a sample graph
        self.graph.nodes["/test/main.ts"] = DependencyNode(
            file_path="/test/main.ts",
            imports=[
                ImportInfo(
                    source_file="/test/main.ts",
                    imported_module="/test/App.vue",
                    import_type=ImportType.DEFAULT,
                    dependency_type=DependencyType.LOCAL,
                    imported_names=["App"],
                    line_number=1,
                ),
                ImportInfo(
                    source_file="/test/main.ts",
                    imported_module="vue",
                    import_type=ImportType.NAMED,
                    dependency_type=DependencyType.EXTERNAL,
                    imported_names=["createApp"],
                    line_number=2,
                ),
            ],
            exports=["main"],
            is_entry_point=True,
            is_leaf_node=False,
        )

        self.graph.nodes["/test/App.vue"] = DependencyNode(
            file_path="/test/App.vue",
            imports=[],
            exports=["App"],
            is_entry_point=False,
            is_leaf_node=True,
        )

        self.graph.graph.add_edge("/test/main.ts", "/test/App.vue")

        stats = self.graph.get_statistics()

        assert stats["total_files"] == 2
        assert stats["total_dependencies"] == 1  # Only local dependencies count
        assert stats["entry_points"] == 1
        assert stats["leaf_nodes"] == 1
        assert stats["circular_dependencies"] == 0
        assert stats["external_packages"] == 1

    def test_find_dependency_path_exists(self):
        """Test finding dependency path when it exists."""
        # Create path: A -> B -> C
        self.graph.graph.add_edge("/test/A.vue", "/test/B.vue")
        self.graph.graph.add_edge("/test/B.vue", "/test/C.vue")

        path = self.graph.find_dependency_path("/test/A.vue", "/test/C.vue")

        assert path is not None
        assert len(path) == 3
        assert path[0] == "/test/A.vue"
        assert path[1] == "/test/B.vue"
        assert path[2] == "/test/C.vue"

    def test_find_dependency_path_not_exists(self):
        """Test finding dependency path when it doesn't exist."""
        # Create disconnected nodes
        self.graph.graph.add_node("/test/A.vue")
        self.graph.graph.add_node("/test/B.vue")

        path = self.graph.find_dependency_path("/test/A.vue", "/test/B.vue")

        assert path is None

    def test_find_dependency_path_same_node(self):
        """Test finding dependency path to same node."""
        self.graph.graph.add_node("/test/A.vue")

        path = self.graph.find_dependency_path("/test/A.vue", "/test/A.vue")

        assert path is not None
        assert len(path) == 1
        assert path[0] == "/test/A.vue"

    def test_analyze_project_integration(self):
        """Test complete project analysis integration."""
        # Create test project structure
        src_dir = Path(self.temp_dir) / "src"
        src_dir.mkdir()

        # Create main.ts
        (src_dir / "main.ts").write_text(
            """
import { createApp } from 'vue'
import App from './App.vue'

createApp(App).mount('#app')
"""
        )

        # Create App.vue
        (src_dir / "App.vue").write_text(
            """
<template>
  <div>
    <UserProfile />
  </div>
</template>

<script>
import UserProfile from './components/UserProfile.vue'

export default {
  name: 'App',
  components: {
    UserProfile
  }
}
</script>
"""
        )

        # Create component
        components_dir = src_dir / "components"
        components_dir.mkdir()
        (components_dir / "UserProfile.vue").write_text(
            """
<template>
  <div>User Profile</div>
</template>

<script>
export default {
  name: 'UserProfile'
}
</script>
"""
        )

        # Mock the parsers to return predictable results
        with (
            patch.object(self.graph.vue_parser, "parse_file") as mock_vue_parse,
            patch.object(self.graph.react_parser, "parse_file") as mock_react_parse,
        ):

            def mock_react_parser(file_path):
                if "main.ts" in file_path:
                    return DependencyNode(
                        file_path=file_path,
                        imports=[
                            ImportInfo(
                                source_file=file_path,
                                imported_module="vue",
                                import_type=ImportType.NAMED,
                                dependency_type=DependencyType.EXTERNAL,
                                imported_names=["createApp"],
                                line_number=1,
                            ),
                            ImportInfo(
                                source_file=file_path,
                                imported_module=str(src_dir / "App.vue"),
                                import_type=ImportType.DEFAULT,
                                dependency_type=DependencyType.LOCAL,
                                imported_names=["App"],
                                line_number=2,
                            ),
                        ],
                        exports=["main"],
                        is_entry_point=True,
                        is_leaf_node=False,
                    )
                return None

            def mock_vue_parser(file_path):
                if "App.vue" in file_path:
                    return DependencyNode(
                        file_path=file_path,
                        imports=[
                            ImportInfo(
                                source_file=file_path,
                                imported_module=str(components_dir / "UserProfile.vue"),
                                import_type=ImportType.DEFAULT,
                                dependency_type=DependencyType.LOCAL,
                                imported_names=["UserProfile"],
                                line_number=1,
                            )
                        ],
                        exports=["App"],
                        is_entry_point=False,
                        is_leaf_node=False,
                    )
                elif "UserProfile.vue" in file_path:
                    return DependencyNode(
                        file_path=file_path,
                        imports=[],
                        exports=["UserProfile"],
                        is_entry_point=False,
                        is_leaf_node=True,
                    )
                return None

            mock_vue_parse.side_effect = mock_vue_parser
            mock_react_parse.side_effect = mock_react_parser

            # Analyze the project
            self.graph.analyze_project()

            # Verify results
            assert len(self.graph.nodes) == 3

            # Check statistics
            stats = self.graph.get_statistics()
            assert stats["total_files"] == 3
            assert stats["entry_points"] == 1
            assert stats["leaf_nodes"] == 1

            # Check entry points
            entry_points = self.graph.get_entry_points()
            assert len(entry_points) == 1
            assert any("main.ts" in ep for ep in entry_points)

            # Check leaf nodes
            leaf_nodes = self.graph.get_leaf_nodes()
            assert len(leaf_nodes) == 1
            assert any("UserProfile.vue" in ln for ln in leaf_nodes)
