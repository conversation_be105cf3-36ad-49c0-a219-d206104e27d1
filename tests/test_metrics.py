"""
Unit tests for the metrics module.

Tests the DependencyMetrics class and its ability to calculate various
project metrics, complexity analysis, and architectural insights.
"""

import tempfile
from pathlib import Path
from unittest.mock import Mock

import pytest

from src.diff_analyzer.dependency_analyzer import DependencyNode, DependencyType, ImportInfo, ImportType
from src.diff_analyzer.dependency_graph import DependencyGraph
from src.diff_analyzer.metrics import DependencyMetrics


class TestDependencyMetrics:
    """Test the DependencyMetrics class."""

    def setup_method(self) -> None:
        """Set up test fixtures."""
        self.temp_dir = Path(tempfile.mkdtemp())
        
        # Create a mock dependency graph
        self.mock_graph = Mock(spec=DependencyGraph)
        self.mock_graph.project_root = self.temp_dir
        
        # Create sample nodes
        self.nodes = {
            "/src/main.ts": DependencyNode(
                file_path="/src/main.ts",
                imports=[
                    ImportInfo(
                        source_file="/src/main.ts",
                        imported_module="/src/App.vue",
                        import_type=ImportType.DEFAULT,
                        dependency_type=DependencyType.LOCAL,
                        imported_names=["App"],
                        line_number=1
                    )
                ],
                exports=["main"],
                is_entry_point=True,
                is_leaf_node=False
            ),
            "/src/App.vue": DependencyNode(
                file_path="/src/App.vue",
                imports=[
                    ImportInfo(
                        source_file="/src/App.vue",
                        imported_module="vue",
                        import_type=ImportType.NAMED,
                        dependency_type=DependencyType.EXTERNAL,
                        imported_names=["createApp"],
                        line_number=1
                    )
                ],
                exports=["App"],
                is_entry_point=False,
                is_leaf_node=True
            )
        }
        
        self.mock_graph.nodes = self.nodes
        
        # Create a mock NetworkX graph
        import networkx as nx
        self.nx_graph = nx.DiGraph()
        self.nx_graph.add_node("/src/main.ts")
        self.nx_graph.add_node("/src/App.vue")
        self.nx_graph.add_edge("/src/main.ts", "/src/App.vue")
        
        self.mock_graph.graph = self.nx_graph

    def teardown_method(self) -> None:
        """Clean up test fixtures."""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)

    def test_metrics_initialization(self) -> None:
        """Test DependencyMetrics initialization."""
        metrics = DependencyMetrics(self.mock_graph)
        
        assert metrics.graph == self.mock_graph
        assert metrics.nodes == self.nodes
        assert metrics.nx_graph == self.nx_graph

    def test_calculate_all_metrics(self) -> None:
        """Test calculating all metrics."""
        metrics = DependencyMetrics(self.mock_graph)
        result = metrics.calculate_all_metrics()
        
        assert isinstance(result, dict)
        assert "basic_metrics" in result
        assert "complexity_metrics" in result
        assert "architectural_metrics" in result
        assert "maintainability_metrics" in result
        assert "quality_metrics" in result
        assert "performance_metrics" in result

    def test_get_basic_metrics(self) -> None:
        """Test basic metrics calculation."""
        metrics = DependencyMetrics(self.mock_graph)
        result = metrics.get_basic_metrics()
        
        assert result["total_files"] == 2
        assert result["total_dependencies"] == 1
        assert result["average_dependencies_per_file"] == 0.5
        assert result["entry_points"] == 1
        assert result["leaf_nodes"] == 1
        assert result["isolated_files"] == 0

    def test_get_basic_metrics_empty_graph(self) -> None:
        """Test basic metrics with empty graph."""
        empty_graph = Mock(spec=DependencyGraph)
        empty_graph.nodes = {}
        import networkx as nx
        empty_graph.graph = nx.DiGraph()
        
        metrics = DependencyMetrics(empty_graph)
        result = metrics.get_basic_metrics()
        
        assert result["total_files"] == 0
        assert result["total_dependencies"] == 0
        assert result["average_dependencies_per_file"] == 0

    def test_get_complexity_metrics(self) -> None:
        """Test complexity metrics calculation."""
        metrics = DependencyMetrics(self.mock_graph)
        result = metrics.get_complexity_metrics()
        
        assert "cyclomatic_complexity" in result
        assert "max_dependency_depth" in result
        assert "average_dependency_depth" in result
        assert "max_fan_in" in result
        assert "max_fan_out" in result
        assert "average_fan_in" in result
        assert "average_fan_out" in result
        
        # Check specific values for our simple graph
        assert result["max_fan_in"] == 1  # App.vue has 1 incoming edge
        assert result["max_fan_out"] == 1  # main.ts has 1 outgoing edge

    def test_get_architectural_metrics(self) -> None:
        """Test architectural metrics calculation."""
        metrics = DependencyMetrics(self.mock_graph)
        result = metrics.get_architectural_metrics()
        
        assert "architectural_patterns" in result
        assert "modularity_score" in result
        assert "most_central_files" in result
        assert "layering_violations" in result
        assert isinstance(result, dict)

    def test_get_maintainability_metrics(self) -> None:
        """Test maintainability metrics calculation."""
        metrics = DependencyMetrics(self.mock_graph)
        result = metrics.get_maintainability_metrics()
        
        assert "change_impact_scores" in result
        assert "maintenance_hotspots" in result
        assert "average_instability" in result
        assert isinstance(result, dict)

    def test_get_quality_metrics(self) -> None:
        """Test quality metrics calculation."""
        metrics = DependencyMetrics(self.mock_graph)
        result = metrics.get_quality_metrics()
        
        assert "code_smells" in result
        assert "import_quality" in result
        assert "best_practices_score" in result
        assert isinstance(result, dict)

    def test_get_performance_metrics(self) -> None:
        """Test performance metrics calculation."""
        metrics = DependencyMetrics(self.mock_graph)
        result = metrics.get_performance_metrics()
        
        assert "estimated_bundle_impact" in result
        assert "loading_performance" in result
        assert "lazy_loading_opportunities" in result
        assert isinstance(result, dict)

    def test_get_file_level_metrics(self) -> None:
        """Test file-level metrics calculation."""
        metrics = DependencyMetrics(self.mock_graph)
        result = metrics.get_file_level_metrics()
        
        assert isinstance(result, dict)
        # Should have metrics for each file
        for file_path in self.nodes.keys():
            assert file_path in result or any(file_path.endswith(key) for key in result.keys())

    def test_get_framework_specific_metrics(self) -> None:
        """Test framework-specific metrics calculation."""
        metrics = DependencyMetrics(self.mock_graph)
        result = metrics.get_framework_specific_metrics()
        
        assert "vue_metrics" in result
        assert "react_metrics" in result
        assert "framework_distribution" in result
        assert isinstance(result, dict)


class TestMetricsWithComplexGraph:
    """Test metrics with more complex graph structures."""

    def setup_method(self) -> None:
        """Set up test fixtures with complex graph."""
        self.temp_dir = Path(tempfile.mkdtemp())
        
        # Create a more complex mock graph
        self.mock_graph = Mock(spec=DependencyGraph)
        self.mock_graph.project_root = self.temp_dir
        
        # Create multiple nodes with various relationships
        self.nodes = {}
        file_paths = [
            "/src/main.ts",
            "/src/App.vue", 
            "/src/components/Header.vue",
            "/src/components/Footer.vue",
            "/src/utils/helpers.ts",
            "/src/store/index.ts"
        ]
        
        for i, path in enumerate(file_paths):
            self.nodes[path] = DependencyNode(
                file_path=path,
                imports=[],
                exports=[f"export{i}"],
                is_entry_point=(i == 0),  # Only main.ts is entry point
                is_leaf_node=(i >= 4)     # utils and store are leaf nodes
            )
        
        self.mock_graph.nodes = self.nodes
        
        # Create NetworkX graph with more complex relationships
        import networkx as nx
        self.nx_graph = nx.DiGraph()
        for path in file_paths:
            self.nx_graph.add_node(path)
        
        # Add edges to create dependencies
        edges = [
            ("/src/main.ts", "/src/App.vue"),
            ("/src/App.vue", "/src/components/Header.vue"),
            ("/src/App.vue", "/src/components/Footer.vue"),
            ("/src/components/Header.vue", "/src/utils/helpers.ts"),
            ("/src/components/Footer.vue", "/src/utils/helpers.ts"),
            ("/src/App.vue", "/src/store/index.ts")
        ]
        
        for source, target in edges:
            self.nx_graph.add_edge(source, target)
        
        self.mock_graph.graph = self.nx_graph

    def teardown_method(self) -> None:
        """Clean up test fixtures."""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)

    def test_complex_basic_metrics(self) -> None:
        """Test basic metrics with complex graph."""
        metrics = DependencyMetrics(self.mock_graph)
        result = metrics.get_basic_metrics()
        
        assert result["total_files"] == 6
        assert result["total_dependencies"] == 6
        assert result["entry_points"] == 1
        assert result["leaf_nodes"] == 2

    def test_complex_complexity_metrics(self) -> None:
        """Test complexity metrics with complex graph."""
        metrics = DependencyMetrics(self.mock_graph)
        result = metrics.get_complexity_metrics()
        
        # App.vue should have highest fan-out (3 dependencies)
        assert result["max_fan_out"] == 3
        # helpers.ts should have highest fan-in (2 dependents)
        assert result["max_fan_in"] == 2

    def test_dependency_depth_calculation(self) -> None:
        """Test dependency depth calculation."""
        metrics = DependencyMetrics(self.mock_graph)
        result = metrics.get_complexity_metrics()
        
        # Should have reasonable depth values
        assert result["max_dependency_depth"] >= 0
        assert result["average_dependency_depth"] >= 0


class TestMetricsEdgeCases:
    """Test metrics calculation edge cases."""

    def test_metrics_with_circular_dependencies(self) -> None:
        """Test metrics calculation with circular dependencies."""
        mock_graph = Mock(spec=DependencyGraph)
        
        # Create nodes with circular dependency
        nodes = {
            "/src/A.vue": DependencyNode("/src/A.vue", [], ["A"], False, False),
            "/src/B.vue": DependencyNode("/src/B.vue", [], ["B"], False, False)
        }
        mock_graph.nodes = nodes
        
        # Create circular graph
        import networkx as nx
        nx_graph = nx.DiGraph()
        nx_graph.add_edge("/src/A.vue", "/src/B.vue")
        nx_graph.add_edge("/src/B.vue", "/src/A.vue")
        mock_graph.graph = nx_graph
        
        metrics = DependencyMetrics(mock_graph)
        result = metrics.get_basic_metrics()
        
        # Should handle circular dependencies gracefully
        assert result["total_files"] == 2
        assert result["total_dependencies"] == 2

    def test_metrics_with_isolated_nodes(self) -> None:
        """Test metrics with isolated nodes."""
        mock_graph = Mock(spec=DependencyGraph)
        
        nodes = {
            "/src/isolated.vue": DependencyNode("/src/isolated.vue", [], ["Isolated"], False, True)
        }
        mock_graph.nodes = nodes
        
        import networkx as nx
        nx_graph = nx.DiGraph()
        nx_graph.add_node("/src/isolated.vue")  # No edges
        mock_graph.graph = nx_graph
        
        metrics = DependencyMetrics(mock_graph)
        result = metrics.get_basic_metrics()
        
        assert result["isolated_files"] == 1
        assert result["total_dependencies"] == 0
