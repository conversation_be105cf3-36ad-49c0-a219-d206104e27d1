"""
Unit tests for the exceptions module.

Tests custom exception classes and their behavior, error message formatting,
and inheritance hierarchy.
"""

import pytest

from src.diff_analyzer.exceptions import (
    CircularDependencyError,
    ConfigurationError,
    DependencyAnalyzerError,
    FileAccessError,
    GraphAnalysisError,
    ImportResolutionError,
    OutputFormattingError,
    ParseError,
    ProjectStructureError,
    TreeSitterError,
    ValidationError,
)


class TestDependencyAnalyzerError:
    """Test the base DependencyAnalyzerError class."""

    def test_basic_error_creation(self) -> None:
        """Test creating a basic error with just a message."""
        error = DependencyAnalyzerError("Test error message")
        assert str(error) == "Test error message"
        assert error.message == "Test error message"
        assert error.file_path is None
        assert error.line_number is None

    def test_error_with_file_path(self) -> None:
        """Test creating an error with file path."""
        error = DependencyAnalyzerError("Test error", file_path="/path/to/file.vue")
        assert "Test error in file: /path/to/file.vue" in str(error)
        assert error.file_path == "/path/to/file.vue"

    def test_error_with_line_number(self) -> None:
        """Test creating an error with line number."""
        error = DependencyAnalyzerError("Test error", line_number=42)
        assert "at line 42" in str(error)
        assert error.line_number == 42

    def test_error_with_file_and_line(self) -> None:
        """Test creating an error with both file path and line number."""
        error = DependencyAnalyzerError(
            "Test error", 
            file_path="/path/to/file.vue", 
            line_number=42
        )
        error_str = str(error)
        assert "Test error in file: /path/to/file.vue" in error_str
        assert "at line 42" in error_str

    def test_error_inheritance(self) -> None:
        """Test that DependencyAnalyzerError inherits from Exception."""
        error = DependencyAnalyzerError("Test")
        assert isinstance(error, Exception)


class TestParseError:
    """Test the ParseError class."""

    def test_parse_error_creation(self) -> None:
        """Test creating a ParseError."""
        error = ParseError("Failed to parse", file_path="/test/file.vue")
        assert isinstance(error, DependencyAnalyzerError)
        assert "Failed to parse" in str(error)
        assert error.file_path == "/test/file.vue"

    def test_parse_error_with_line_number(self) -> None:
        """Test ParseError with line number."""
        error = ParseError("Syntax error", file_path="/test/file.vue", line_number=10)
        error_str = str(error)
        assert "Syntax error" in error_str
        assert "at line 10" in error_str


class TestImportResolutionError:
    """Test the ImportResolutionError class."""

    def test_import_resolution_error_creation(self) -> None:
        """Test creating an ImportResolutionError."""
        error = ImportResolutionError(
            "Cannot resolve import",
            import_path="./components/Button",
            file_path="/src/App.vue"
        )
        assert isinstance(error, DependencyAnalyzerError)
        assert error.import_path == "./components/Button"
        error_str = str(error)
        assert "Cannot resolve import: './components/Button'" in error_str

    def test_import_resolution_error_with_line(self) -> None:
        """Test ImportResolutionError with line number."""
        error = ImportResolutionError(
            "Cannot resolve",
            import_path="@/utils",
            file_path="/src/main.ts",
            line_number=5
        )
        error_str = str(error)
        assert "Cannot resolve: '@/utils'" in error_str
        assert "at line 5" in error_str


class TestCircularDependencyError:
    """Test the CircularDependencyError class."""

    def test_circular_dependency_error_creation(self) -> None:
        """Test creating a CircularDependencyError."""
        cycle = ["/src/A.vue", "/src/B.vue", "/src/A.vue"]
        error = CircularDependencyError(cycle)
        assert isinstance(error, DependencyAnalyzerError)
        assert error.cycle == cycle
        error_str = str(error)
        assert "Circular dependency detected" in error_str
        assert "/src/A.vue -> /src/B.vue -> /src/A.vue" in error_str

    def test_circular_dependency_error_empty_cycle(self) -> None:
        """Test CircularDependencyError with empty cycle."""
        error = CircularDependencyError([])
        assert error.cycle == []
        assert "Circular dependency detected" in str(error)

    def test_circular_dependency_error_single_file(self) -> None:
        """Test CircularDependencyError with single file cycle."""
        cycle = ["/src/self-import.vue"]
        error = CircularDependencyError(cycle)
        error_str = str(error)
        assert "/src/self-import.vue" in error_str


class TestFileAccessError:
    """Test the FileAccessError class."""

    def test_file_access_error_creation(self) -> None:
        """Test creating a FileAccessError."""
        error = FileAccessError("Permission denied", file_path="/protected/file.vue")
        assert isinstance(error, DependencyAnalyzerError)
        assert error.file_path == "/protected/file.vue"
        assert "Permission denied" in str(error)

    def test_file_access_error_with_file_path(self) -> None:
        """Test FileAccessError with file path."""
        error = FileAccessError(
            "Cannot read file",
            file_path="/missing/file.vue"
        )
        assert error.file_path == "/missing/file.vue"
        error_str = str(error)
        assert "Cannot read file" in error_str
        assert "/missing/file.vue" in error_str


class TestConfigurationError:
    """Test the ConfigurationError class."""

    def test_configuration_error_creation(self) -> None:
        """Test creating a ConfigurationError."""
        error = ConfigurationError("Invalid configuration")
        assert isinstance(error, DependencyAnalyzerError)
        assert "Invalid configuration" in str(error)

    def test_configuration_error_with_file_path(self) -> None:
        """Test ConfigurationError with file path."""
        error = ConfigurationError("Invalid value", file_path="/config/file.json")
        assert error.file_path == "/config/file.json"
        error_str = str(error)
        assert "Invalid value" in error_str
        assert "/config/file.json" in error_str


class TestGraphAnalysisError:
    """Test the GraphAnalysisError class."""

    def test_graph_analysis_error_creation(self) -> None:
        """Test creating a GraphAnalysisError."""
        error = GraphAnalysisError("Graph analysis failed")
        assert isinstance(error, DependencyAnalyzerError)
        assert "Graph analysis failed" in str(error)


class TestOutputFormattingError:
    """Test the OutputFormattingError class."""

    def test_output_formatting_error_creation(self) -> None:
        """Test creating an OutputFormattingError."""
        error = OutputFormattingError("Formatting failed", format_type="json")
        assert isinstance(error, DependencyAnalyzerError)
        assert error.format_type == "json"
        error_str = str(error)
        assert "Formatting failed for format: json" in error_str

    def test_output_formatting_error_different_format(self) -> None:
        """Test OutputFormattingError with different format."""
        error = OutputFormattingError("DOT generation failed", format_type="dot")
        assert error.format_type == "dot"
        assert "for format: dot" in str(error)


class TestValidationError:
    """Test the ValidationError class."""

    def test_validation_error_creation(self) -> None:
        """Test creating a ValidationError."""
        error = ValidationError("Validation failed")
        assert isinstance(error, DependencyAnalyzerError)
        assert "Validation failed" in str(error)


class TestTreeSitterError:
    """Test the TreeSitterError class."""

    def test_tree_sitter_error_creation(self) -> None:
        """Test creating a TreeSitterError."""
        error = TreeSitterError("Tree-sitter parsing failed")
        assert isinstance(error, DependencyAnalyzerError)
        assert "Tree-sitter parsing failed" in str(error)


class TestProjectStructureError:
    """Test the ProjectStructureError class."""

    def test_project_structure_error_creation(self) -> None:
        """Test creating a ProjectStructureError."""
        error = ProjectStructureError("Invalid project structure")
        assert isinstance(error, DependencyAnalyzerError)
        assert "Invalid project structure" in str(error)


class TestExceptionHierarchy:
    """Test the exception inheritance hierarchy."""

    def test_all_exceptions_inherit_from_base(self) -> None:
        """Test that all custom exceptions inherit from DependencyAnalyzerError."""
        exception_classes = [
            ParseError,
            ImportResolutionError,
            CircularDependencyError,
            FileAccessError,
            ConfigurationError,
            GraphAnalysisError,
            OutputFormattingError,
            ValidationError,
            TreeSitterError,
            ProjectStructureError,
        ]

        for exc_class in exception_classes:
            # Create instance with minimal required args
            if exc_class == ImportResolutionError:
                instance = exc_class("test", "import_path")
            elif exc_class == CircularDependencyError:
                instance = exc_class([])
            elif exc_class == OutputFormattingError:
                instance = exc_class("test", "format")
            else:
                instance = exc_class("test")

            assert isinstance(instance, DependencyAnalyzerError)
            assert isinstance(instance, Exception)


class TestExceptionUsagePatterns:
    """Test common exception usage patterns."""

    def test_exception_chaining(self) -> None:
        """Test exception chaining with raise from."""
        original_error = ValueError("Original error")
        
        try:
            raise DependencyAnalyzerError("Wrapped error") from original_error
        except DependencyAnalyzerError as e:
            assert e.__cause__ is original_error

    def test_exception_in_context_manager(self) -> None:
        """Test using exceptions in context managers."""
        with pytest.raises(ParseError):
            raise ParseError("Test parse error")

    def test_exception_message_formatting(self) -> None:
        """Test that exception messages are properly formatted."""
        error = ImportResolutionError(
            "Cannot resolve import",
            import_path="./missing",
            file_path="/src/test.vue",
            line_number=10
        )
        
        message = str(error)
        assert "Cannot resolve import: './missing'" in message
        assert "in file: /src/test.vue" in message
        assert "at line 10" in message
