"""
Pytest configuration and shared fixtures for the test suite.
"""

import tempfile
from pathlib import Path
from typing import Any, Dict, Generator

import pytest

from src.diff_analyzer.dependency_analyzer import (
    DependencyNode,
    DependencyType,
    ImportInfo,
    ImportType,
)
from src.diff_analyzer.dependency_graph import DependencyGraph


@pytest.fixture
def temp_project_dir() -> Generator[Path, None, None]:
    """Create a temporary directory for test projects."""
    with tempfile.TemporaryDirectory() as temp_dir:
        yield Path(temp_dir)


@pytest.fixture
def sample_vue_file_content() -> str:
    """Sample Vue.js single file component content."""
    return """<template>
  <div class="user-profile">
    <UserCard :user="user" />
    <ContactInfo :contact="user.contact" />
  </div>
</template>

<script lang="ts">
import { defineComponent, ref } from 'vue'
import UserCard from './UserCard.vue'
import ContactInfo from '../common/ContactInfo.vue'
import { User } from '@/types/user'
import { apiService } from '@/services/api'

export default defineComponent({
  name: 'UserProfile',
  components: {
    UserCard,
    ContactInfo
  },
  setup() {
    const user = ref<User | null>(null)
    return {
      user
    }
  }
})
</script>

<style scoped>
.user-profile {
  padding: 20px;
}
</style>"""


@pytest.fixture
def sample_react_file_content() -> str:
    """Sample React component content."""
    return """import React, { useState, useEffect } from 'react';
import { UserCard } from './UserCard';
import { ContactInfo } from '../common/ContactInfo';
import { User } from '../types/user';
import { apiService } from '../services/api';

interface UserProfileProps {
  userId: string;
}

export const UserProfile: React.FC<UserProfileProps> = ({ userId }) => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchUser = async () => {
      try {
        const userData = await apiService.getUser(userId);
        setUser(userData);
      } catch (error) {
        console.error('Failed to fetch user:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchUser();
  }, [userId]);

  if (loading) {
    return <div>Loading...</div>;
  }

  if (!user) {
    return <div>User not found</div>;
  }

  return (
    <div className="user-profile">
      <UserCard user={user} />
      <ContactInfo contact={user.contact} />
    </div>
  );
};

export default UserProfile;"""


@pytest.fixture
def sample_import_info() -> ImportInfo:
    """Sample ImportInfo object for testing."""
    return ImportInfo(
        source_file="/test/src/components/UserProfile.vue",
        imported_module="/test/src/components/UserCard.vue",
        import_type=ImportType.DEFAULT,
        dependency_type=DependencyType.LOCAL,
        imported_names=["UserCard"],
        line_number=10,
        is_type_only=False,
    )


@pytest.fixture
def sample_dependency_node() -> DependencyNode:
    """Sample DependencyNode object for testing."""
    imports = [
        ImportInfo(
            source_file="/test/src/App.vue",
            imported_module="/test/src/components/UserProfile.vue",
            import_type=ImportType.DEFAULT,
            dependency_type=DependencyType.LOCAL,
            imported_names=["UserProfile"],
            line_number=5,
            is_type_only=False,
        )
    ]

    return DependencyNode(
        file_path="/test/src/App.vue",
        imports=imports,
        exports=["App"],
        is_entry_point=True,
        is_leaf_node=False,
    )


@pytest.fixture
def mock_project_structure(temp_project_dir: Path) -> Dict[str, Any]:
    """Create a mock project structure for testing."""
    # Create Vue project structure
    vue_src = temp_project_dir / "vue_project" / "src"
    vue_src.mkdir(parents=True)

    # Create React project structure
    react_src = temp_project_dir / "react_project" / "src"
    react_src.mkdir(parents=True)

    # Vue files
    (vue_src / "main.ts").write_text(
        """
import { createApp } from 'vue'
import App from './App.vue'
import router from './router'

createApp(App).use(router).mount('#app')
"""
    )

    (vue_src / "App.vue").write_text(
        """
<template>
  <div id="app">
    <router-view />
  </div>
</template>

<script lang="ts">
import { defineComponent } from 'vue'

export default defineComponent({
  name: 'App'
})
</script>
"""
    )

    # React files
    (react_src / "index.tsx").write_text(
        """
import React from 'react';
import ReactDOM from 'react-dom/client';
import App from './App';

const root = ReactDOM.createRoot(document.getElementById('root')!);
root.render(<App />);
"""
    )

    (react_src / "App.tsx").write_text(
        """
import React from 'react';
import { UserProfile } from './components/UserProfile';

const App: React.FC = () => {
  return (
    <div className="app">
      <UserProfile userId="123" />
    </div>
  );
};

export default App;
"""
    )

    return {
        "temp_dir": temp_project_dir,
        "vue_project": temp_project_dir / "vue_project",
        "react_project": temp_project_dir / "react_project",
        "vue_src": vue_src,
        "react_src": react_src,
    }


@pytest.fixture
def dependency_graph_with_data(mock_project_structure: Dict[str, Any]) -> DependencyGraph:
    """Create a DependencyGraph with sample data."""
    vue_project = mock_project_structure["vue_project"]
    graph = DependencyGraph(str(vue_project))

    # Add some mock nodes for testing
    main_node = DependencyNode(
        file_path=str(vue_project / "src" / "main.ts"),
        imports=[],
        exports=["main"],
        is_entry_point=True,
        is_leaf_node=False,
    )

    app_node = DependencyNode(
        file_path=str(vue_project / "src" / "App.vue"),
        imports=[],
        exports=["App"],
        is_entry_point=False,
        is_leaf_node=True,
    )

    graph.nodes[main_node.file_path] = main_node
    graph.nodes[app_node.file_path] = app_node

    # Add to NetworkX graph
    graph.graph.add_node(main_node.file_path)
    graph.graph.add_node(app_node.file_path)
    graph.graph.add_edge(main_node.file_path, app_node.file_path)

    return graph
