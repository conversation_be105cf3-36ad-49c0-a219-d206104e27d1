{"// Configuration file for Vue.js and React Dependency Analyzer": "", "// Copy this file to .dependency-analyzer.json and customize as needed": "", "// Performance settings": "", "max_workers": 4, "parallel_processing": true, "cache_enabled": true, "cache_dir": ".dependency_cache", "max_file_size": 10485760, "// File processing": "", "exclude_patterns": ["node_modules", ".git", "dist", "build", ".next", ".nuxt", "coverage", "*.min.js", "*.bundle.js"], "supported_extensions": [".vue", ".jsx", ".tsx", ".ts", ".js"], "// Output settings": "", "output_format": "text", "verbose": false, "show_external_deps": false, "show_details": false, "find_cycles": true, "mermaid_type": "flowchart", "// Analysis rules": "", "analysis_rules": {"max_dependency_depth": 50, "warn_circular_deps": true, "warn_large_files": true, "warn_many_deps": 20, "ignore_test_files": true, "ignore_story_files": true}, "// Output formatting options": "", "output_options": {"include_line_numbers": true, "include_file_sizes": false, "include_timestamps": false, "sort_dependencies": true, "group_by_type": false, "show_import_types": true}, "// Custom parser configurations": "", "custom_parsers": {"// Example: Add support for custom file types": "", ".custom": {"parser_class": "CustomParser", "import_patterns": ["import.*from.*['\"](.+)['\"]"]}}, "// Framework-specific settings": "", "vue_settings": {"parse_template_deps": true, "parse_style_deps": false, "component_name_patterns": ["export default", "Vue.component"]}, "react_settings": {"detect_hooks": true, "detect_hoc": true, "jsx_pragma": "React.createElement"}, "// Integration settings": "", "integrations": {"eslint": {"enabled": false, "config_file": ".eslintrc.js"}, "typescript": {"enabled": true, "config_file": "tsconfig.json"}, "webpack": {"enabled": false, "config_file": "webpack.config.js"}}, "// Reporting settings": "", "reporting": {"generate_metrics": true, "include_complexity": false, "include_maintainability": false, "export_formats": ["json", "csv"]}}